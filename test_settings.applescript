-- Тест для открытия настроек uProd
tell application "uProd"
    activate
end tell

delay 1

-- Попробуем открыть настройки через горячую клавишу
tell application "System Events"
    tell process "uProd"
        key code 44 using command down -- Cmd+,
    end tell
end tell

delay 2

-- Проверим, открылось ли окно настроек
tell application "System Events"
    tell process "uProd"
        if exists window "Настройки" then
            log "✅ Окно настроек открыто"
            
            -- Попробуем найти кнопку "Тест"
            try
                click button "Тест" of window "Настройки"
                log "✅ Кнопка 'Тест' нажата"
                delay 3
                
                -- Проверим, появилось ли окно yellow zone
                if exists window 1 then
                    log "✅ Появилось окно (возможно yellow zone)"
                else
                    log "❌ Окно не появилось"
                end if
                
            on error
                log "❌ Кнопка 'Тест' не найдена"
            end try
            
        else
            log "❌ Окно настроек не найдено"
        end if
    end tell
end tell
