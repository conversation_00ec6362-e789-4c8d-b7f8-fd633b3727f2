# Единая система напоминаний uProd

## 🎯 Цель
Создать полностью унифицированную систему напоминаний для всех случаев:
- Формальные интервалы (Pomodoro завершен)
- Неформальные сессии (обнаружена работа без таймера)
- Continuous work reminders (эскалация напоминаний)

## 🏗️ Архитектура

### Принцип DRY применяется ко ВСЕМУ:
- ✅ Визуальная часть (окна, кнопки, позиционирование)
- ✅ Логика таймеров и эскалации
- ✅ Проверка активности (для ВСЕХ случаев)
- ✅ Система уровней и анимаций

## 📊 Единая система уровней

### Уровень 0 (Зеленый)
- **Триггер**: Первое появление окна
- **Анимация**: Плавное появление без тряски
- **Сообщение**:
  - Формальные интервалы: "🎉 Session completed!" / "What's next?"
  - Неформальные напоминания: "🌿 Time for rest" / "Using laptop 52+ minutes"

### Уровень 1 (Желтый) - Yellow Zone
- **Триггер**: Через 1 минуту после "I need a couple of minutes"
- **Анимация**:
  - **Если окно уже открыто**: Легкая тряска на месте
  - **Если окно было закрыто**: Появляется БЕЗ тряски с правильным текстом
- **Сообщение**: "⚠️ You've been working for 57+ minutes"

### Уровень 2 (Оранжевый)
- **Триггер**: Через 2 минуты после уровня 1
- **Анимация**:
  - **Если окно уже открыто**: Средняя тряска на месте
  - **Если окно было закрыто**: Появляется БЕЗ тряски с правильным текстом
- **Сообщение**: "🔥 You've been working for 62+ minutes"

### Уровень 3+ (Красный)
- **Триггер**: Через 3 минуты после уровня 2, затем каждые 5 минут
- **Анимация**:
  - **Если окно уже открыто**: Сильная тряска + увеличивающаяся интенсивность
  - **Если окно было закрыто**: Появляется БЕЗ тряски с правильным текстом
- **Сообщение**: "🚨 You've been working for 67+ minutes"

## 🎨 Логика анимаций

### Принцип работы анимаций:
1. **Тряска только для уже открытых окон** - чтобы привлечь внимание к существующему окну
2. **Новые окна всегда без тряски** - потому что пользователь уже скрыл предыдущее окно

### Методы обновления окон:
- **`updateMessage(reminderCount: Int)`** - обновляет существующее окно С тряской
- **`updateMessageWithoutShake(reminderCount: Int)`** - обновляет текст БЕЗ тряски (для новых окон)

### Логика в `handleOvertimeColorChange()`:
```swift
if let window = modernCompletionWindow {
    // Окно уже открыто → обновляем С тряской
    window.updateMessage(reminderCount: colorLevel)
} else if colorLevel > 0 {
    // Окно было закрыто → показываем новое БЕЗ тряски
    showCompletionWindow()
    modernCompletionWindow?.updateMessageWithoutShake(reminderCount: colorLevel)
}
```

### Логика в `showUnifiedReminder()`:
```swift
if let existingWindow = modernCompletionWindow {
    // Окно уже существует → просто обновляем его (как для формальных интервалов)
    existingWindow.updateMessage(reminderCount: level)
    return
}
// Создаем новое окно только если его еще нет
```

## 🔄 Единая логика работы

### 1. Показ первого окна
```
Любой триггер → showUnifiedReminder(type, level: 0)
```

### 2. Обработка "Take a break"
```
Закрыть окно → Начать отдых → Остановить все таймеры
```

### 3. Обработка "I need a couple of minutes"
```
Закрыть окно → startUnifiedEscalationSystem(fromLevel: 1)
```

### 4. Система эскалации
```
Каждую минуту:
1. Проверить активность пользователя
2. Если НЕТ активности → остановить таймер
3. Если ЕСТЬ активность → продолжить эскалацию
4. Показать окно следующего уровня (с правильной анимацией)
```

## 🎮 Единая система проверки активности

### ✅ UnifiedActivityChecker - DRY принцип соблюден!

**Единый класс для ВСЕХ типов проверки активности:**
- `UnifiedActivityChecker.shared.isUserCurrentlyActive()` - активен ли пользователь сейчас (15 сек)
- `UnifiedActivityChecker.shared.wasActiveInLastMinute()` - была ли активность за минуту
- `UnifiedActivityChecker.shared.isAppIdle()` - находится ли приложение в состоянии покоя

### Единые правила для ВСЕХ случаев:
- Отслеживание через macOS CGEventSource API (мышь, клавиатура, клики, скролл)
- Дополнительная проверка активных медиа-приложений
- Проверка при каждом изменении уровня эскалации
- **Если пользователь неактивен** → НЕ показывать следующий уровень эскалации
- **При возобновлении активности** → продолжить эскалацию с текущего уровня

### Использование в коде:

**Формальные интервалы:**
```swift
// В PomodoroTimer.updateOvertimeTimer()
pomodoroTimer.onCheckUserActivity = {
    return UnifiedActivityChecker.shared.isUserCurrentlyActive()
}
```

**Неформальные интервалы:**
```swift
// В AppDelegate.startUnifiedReminderSystem()
let shouldShowReminder = UnifiedActivityChecker.shared.isUserCurrentlyActive()
```

**Детектор неформальных сессий:**
```swift
// В InformalSessionDetector.isAppIdle()
return UnifiedActivityChecker.shared.isAppIdle()
```

### Конфигурация системы:

**Настраиваемые параметры в `UnifiedActivityChecker`:**
- `activityThreshold = 15.0` секунд - порог текущей активности
- `recentActivityThreshold = 60.0` секунд - порог недавней активности

**Что отслеживается:**
- Движения мыши (с фильтрацией микро-движений)
- Нажатия клавиш
- Клики мыши
- Скроллинг
- Активные медиа-приложения (Safari, Chrome, VLC, Spotify и др.)

**Логика определения активности:**
- **Активен сейчас**: активность за последние 15 секунд
- **Активен недавно**: активность за последнюю минуту
- **Приложение idle**: НЕТ активности за последние 15 секунд

### Преимущества единой системы:
- ✅ **DRY принцип** - одно место для всех изменений
- ✅ **Консистентность** - одинаковая логика для всех компонентов
- ✅ **Легкость настройки** - один класс для всех параметров активности
- ✅ **Отладка** - единое место для логирования и диагностики

### 🧪 Тестирование системы активности:

**Через меню приложения:**
- **"🔍 Тест активности"** - показывает текущее состояние всех параметров активности
- **"Тест эскалации"** - автоматический тест эскалации с проверкой активности

**Методы тестирования в коде:**
```swift
// Получить детальную информацию о состоянии
let debugInfo = UnifiedActivityChecker.shared.getDebugInfo()

// Принудительно установить результат для тестирования
UnifiedActivityChecker.shared.setForcedActivityResult(false) // имитация неактивности
UnifiedActivityChecker.shared.setForcedActivityResult(nil)   // сброс принудительного результата
```

**Сценарий тестирования "ушел-вернулся":**
1. Запустить тестовый интервал (3 сек)
2. Дождаться завершения → "Session completed!"
3. Нажать "I need a couple of minutes"
4. НЕ двигать мышью 20+ секунд → система считает неактивным
5. Подвигать мышью → показывается актуальный уровень эскалации

**Тестирование сброса после длительного сна:**
- **"💤 Тест длительного сна"** - симулирует сон >15 минут и автоматический сброс всех состояний

**Тестирование единства логики отдыха:**
- **"🧪 Тест неформального отдыха"** - проверяет единство логики для неформальных интервалов

## 💤 Система сброса состояний после длительного сна

### Проблема:
Пользователь закрыл ноутбук с активным окном (Red Zone, Yellow Zone и т.д.), а утром открыл и видит устаревшее окно. Непонятно, что делать с этим сообщением.

### Решение:
**Автоматический сброс всех состояний после сна >17 минут:**

### Что отслеживается:
- `NSWorkspace.willSleepNotification` - система засыпает (закрытие крышки)
- `NSWorkspace.didWakeNotification` - система просыпается (открытие крышки)
- Длительность сна рассчитывается автоматически

### Умные пороги сна:
- **1-10 минут**: Короткий сон - ничего не сбрасывается
- **10-15 минут**: Средний сон - сбрасывается только активность (`ComputerTimeTracker`)
- **15+ минут**: Длительный сон - сбрасываются ВСЕ состояния и окна (`AppDelegate`)

### Что сбрасывается при длительном сне (>15 мин):
1. **Все открытые окна:**
   - `modernCompletionWindow` (Red Zone, Yellow Zone и др.)
   - `breakStartWindow`, `breakEndWindow`, `breakTypeWindow`

2. **Состояние таймеров:**
   - Переработка (`overtime`) → возврат в `idle`
   - Все системы отслеживания остановлены

3. **Флаги состояний:**
   - `userPromisedToRest = false`
   - `userRestingAtComputer = false`
   - `isBreakPostponed = false`

4. **Активные таймеры:**
   - `postponeTimer`, `postponeUpdateTimer`, `continuousWorkTimer`

5. **История активности:**
   - Сброс через `informalSessionDetector.resetActivityHistory()`

### Логика работы:
```swift
// При пробуждении системы
let sleepDuration = Date().timeIntervalSince(sleepStartTime)
let longSleepThreshold: TimeInterval = 15 * 60 // 15 минут

if sleepDuration > longSleepThreshold {
    resetAllStatesAfterLongSleep()
}
```

### Преимущества:
- ✅ **Чистое состояние** после длительного отсутствия
- ✅ **Нет устаревших окон** при открытии ноутбука утром
- ✅ **Логичное поведение** - длительный сон = полный сброс
- ✅ **Настраиваемый порог** (15 минут по умолчанию)

## 🎯 Унифицированная логика отдыха

### Проблема:
Раньше формальные и неформальные интервалы имели разную логику выбора типа отдыха:
- **Формальные**: проверяли время (11:00-13:00) и количество интервалов → могли показать длинный отдых
- **Неформальные**: всегда только короткий отдых

### Решение:
**Единая логика для ВСЕХ типов интервалов:**

### Унифицированный выбор типа отдыха:
```swift
// Для ВСЕХ типов интервалов (формальных и неформальных)
if pomodoroTimer.shouldShowBreakTypeSelection() {
    showBreakTypeSelectionWindow() // Выбор длинный/короткий
} else {
    startBreakWithType(isLong: false) // Автоматически короткий
}
```

### Условия для длинного отдыха (едины для всех):
1. **Количество интервалов:**
   - Первый цикл: 3 интервала
   - Последующие циклы: 2 интервала

2. **Время дня (будущая функция):**
   - 11:00-13:00 (обеденное время)

### Единая система активности во время отдыха:
- **Один BreakTimer** для всех типов отдыха
- **Единая проверка активности** через `UnifiedActivityChecker`
- **Одинаковые уведомления** об активности:
  - Если пользователь "обещал отдыхать" → строгое предупреждение
  - Если "отдыхает за компом" → уведомления отключены
  - Обычный режим → стандартное предупреждение

### Полное единство ВСЕГО цикла отдыха:

**1. Начало отдыха:**
- ✅ Одинаковый выбор длинный/короткий для всех типов интервалов
- ✅ Одинаковые условия (3 интервала → длинный отдых)

**2. Во время отдыха:**
- ✅ Одинаковые уведомления об активности ("закройте ноутбук и отдохните")
- ✅ Одинаковые флаги поведения (обещал отдыхать / отдыхаю за компом)

**3. Завершение отдыха:**
- ✅ Одинаковое окно "Start New Session"
- ✅ Одинаковый зеленый счетчик отложенного отдыха
- ✅ Одинаковая система эскалации после отдыха

### Преимущества:
- ✅ **Полная консистентность** - ВЕСЬ цикл отдыха одинаков для всех интервалов
- ✅ **Справедливость** - неформальные интервалы получают все возможности формальных
- ✅ **Простота** - одна логика вместо множества разных систем
- ✅ **Предсказуемость** - пользователь всегда знает, чего ожидать

## 🎯 Пример сценария работы

### Сценарий: Пользователь ушел во время эскалации

1. **Интервал завершен** → Показывается окно "Session completed!" (уровень 0)
2. **Пользователь нажимает "I need a couple of minutes"** → Окно закрывается, начинается эскалация
3. **Через 1 минуту** → Система проверяет активность:
   - **Если активен**: Показывается желтое окно (уровень 1) с тряской
   - **Если неактивен**: Эскалация НЕ показывается, таймер продолжает идти
4. **Пользователь ушел в туалет на 3 минуты** → Система не показывает оранжевое/красное окно
5. **Пользователь вернулся и начал работать** → При следующем изменении уровня:
   - Система обнаруживает активность
   - Показывается окно с текущим уровнем эскалации (например, красное, если прошло достаточно времени)
   - Эскалация продолжается с актуального уровня

### Принцип:
- **Таймер переработки всегда идет** (время учитывается)
- **Окна показываются только при активности** (не беспокоим отсутствующего пользователя)
- **При возвращении показывается актуальный уровень** (не начинаем сначала)

## 🔧 Техническая реализация

### Единый enum для всех типов:
```swift
enum ReminderType {
    case formalIntervalCompleted    // Формальный интервал завершен
    case informalWorkDetected       // Обнаружена неформальная работа
    case continuousWorkReminder(minutes: Int, level: Int) // Напоминания о непрерывной работе
}
```

### Единый метод эскалации:
```swift
func startUnifiedEscalationSystem(fromLevel: Int = 1)
```

### Единая проверка активности:
```swift
func checkActivityAndContinueEscalation()
```

## 📱 Единые окна и кнопки

### Все окна используют:
- `ModernCompletionWindow`
- Режимы: `.sessionCompleted` (формальные) или `.restSuggestion` (неформальные)
- Единое позиционирование `positionWindow()`
- Единую анимацию `showAppearanceAnimation()`

### Кнопки в зависимости от типа:
**Формальные интервалы (.sessionCompleted):**
- **"Take a break"** - зеленая градиентная кнопка (`onComplete`)
- **"I need a couple of minutes"** - желтая кнопка с dash-обводкой (`onExtend1`)

**Неформальные напоминания (.restSuggestion):**
- **"Take a break"** - зеленая градиентная кнопка (`onTakeBreak`)
- **"I need a couple of minutes"** - желтая кнопка с dash-обводкой (`onContinueWorking`)

## 🎯 План внедрения

### Этап 1: Исправить визуальные проблемы ✅
- [x] Убрать дерганье окон
- [x] Исправить позиционирование
- [x] Добавить проверку активности в единую систему
- [x] Исправить двойное появление окон при завершении формальных интервалов
- [x] Настроить правильные режимы окон (.sessionCompleted vs .restSuggestion)
- [x] Исправить появление yellow zone для неформальных окон
- [x] Унифицировать логику анимаций для всех типов окон
- [x] Реализовать правильную логику тряски (только для уже открытых окон)
- [x] Исправить пересоздание окон в `showUnifiedReminder()`
- [x] Создать единую систему проверки активности (UnifiedActivityChecker)
- [x] Интегрировать единую систему во все компоненты (DRY принцип)
- [x] Унифицировать логику проверки активности для всех типов интервалов

### Этап 2: Создать единую систему эскалации
- [ ] Создать `UnifiedReminderSystem` класс
- [ ] Интегрировать проверку активности
- [ ] Унифицировать все вызовы

### Этап 3: Тестирование и отладка
- [ ] Протестировать формальные интервалы
- [ ] Протестировать неформальные сессии
- [ ] Протестировать систему эскалации

### Этап 4: Очистка кода
- [ ] Удалить старые системы
- [ ] Обновить документацию
- [ ] Финальное тестирование

## 🚀 Преимущества единой системы

1. **Принцип DRY соблюден полностью**
2. **Одно место для всех изменений**
3. **Логичное поведение для всех случаев**
4. **Проще поддерживать и расширять**
5. **Единообразный UX**

## 📝 Текущий статус

- ✅ Визуальная унификация (окна, кнопки, позиционирование)
- ✅ Правильные режимы окон для разных типов напоминаний
- ✅ Исправлена проблема двойного показа окон
- ✅ Унифицированная логика анимаций (DRY принцип соблюден)
- ✅ Правильная логика тряски для всех типов окон
- ✅ Исправлено пересоздание окон в неформальных напоминаниях
- ✅ Единая система проверки активности (UnifiedActivityChecker)
- ✅ Интеграция проверки активности во все компоненты
- ✅ Инструменты тестирования системы активности
- ✅ Система сброса состояний после длительного сна (>15 мин)
- ✅ Полностью унифицированная логика отдыха (формальные + неформальные)
- ✅ Единая система выбора типа отдыха для всех интервалов
- ✅ Единая система активности во время отдыха для всех интервалов
- ✅ Единая система завершения отдыха для всех интервалов
- ⚠️ Частичная логическая унификация системы эскалации

## 🔍 Следующие шаги

1. Создать полностью единую систему эскалации (UnifiedReminderSystem)
2. Объединить логику формальных и неформальных интервалов в один класс
3. Протестировать edge cases и граничные условия
4. Финальная очистка кода и удаление дублирующих систем
