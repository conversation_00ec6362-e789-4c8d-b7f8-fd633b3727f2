import Foundation
import Cocoa

/// Обработчик крашей приложения
class CrashHandler {
    static let shared = CrashHandler()
    
    private var isSetup = false
    
    private init() {}
    
    /// Настройка обработчиков крашей
    func setup() {
        guard !isSetup else { return }
        isSetup = true
        
        logInfo("Crash", "🛡️ Настройка защиты от крашей")
        
        // Обработчик необработанных исключений
        NSSetUncaughtExceptionHandler { exception in
            CrashHandler.shared.handleException(exception)
        }
        
        // Обработчики системных сигналов
        setupSignalHandlers()
        
        // Обработчик завершения приложения
        setupTerminationHandler()
        
        logInfo("Crash", "✅ Защита активирована")
    }
    
    /// Обработка необработанного исключения
    private func handleException(_ exception: NSException) {
        let message = "💥 Exception: \(exception.name.rawValue)"
        Logger.shared.logCrash(message, exception: exception)

        // Показываем пользователю информацию о краше
        DispatchQueue.main.async {
            self.showCrashDialog(message: "Приложение столкнулось с критической ошибкой и будет закрыто.")
        }

        // Даем время на запись лога
        Thread.sleep(forTimeInterval: 1.0)

        // Завершаем приложение
        exit(1)
    }
    
    /// Обработка системного сигнала
    private func handleSignal(_ signal: Int32) {
        let message = "⚡ Signal \(signal) (\(getSignalName(signal)))"
        Logger.shared.logCrash(message, signal: signal)

        // Показываем пользователю информацию о краше
        DispatchQueue.main.async {
            self.showCrashDialog(message: "Приложение было принудительно завершено системой.")
        }

        // Даем время на запись лога
        Thread.sleep(forTimeInterval: 1.0)

        // Восстанавливаем стандартный обработчик и повторно отправляем сигнал
        Darwin.signal(signal, SIG_DFL)
        raise(signal)
    }
    
    /// Настройка обработчиков сигналов
    private func setupSignalHandlers() {
        let signals: [Int32] = [SIGABRT, SIGSEGV, SIGBUS, SIGILL, SIGFPE, SIGPIPE]
        
        for sig in signals {
            Darwin.signal(sig) { signal in
                CrashHandler.shared.handleSignal(signal)
            }
        }
    }
    
    /// Настройка обработчика завершения приложения
    private func setupTerminationHandler() {
        // Обработчик нормального завершения
        atexit {
            logInfo("App", "👋 Нормальное завершение")
        }

        // Обработчик уведомлений о завершении
        NotificationCenter.default.addObserver(
            forName: NSApplication.willTerminateNotification,
            object: nil,
            queue: .main
        ) { _ in
            logInfo("App", "🔄 Получен сигнал завершения")
            Logger.shared.logMemoryUsage()
        }
    }
    
    /// Показ диалога о краше пользователю
    private func showCrashDialog(message: String) {
        let alert = NSAlert()
        alert.messageText = "Критическая ошибка"
        alert.informativeText = message + "\n\nЛоги сохранены для анализа."
        alert.alertStyle = .critical
        alert.addButton(withTitle: "OK")
        
        // Показываем диалог в главном потоке
        if Thread.isMainThread {
            alert.runModal()
        } else {
            DispatchQueue.main.sync {
                alert.runModal()
            }
        }
    }
}

/// Расширение для логирования состояния приложения
extension CrashHandler {
    
    /// Логирование текущего состояния приложения
    func logApplicationState() {
        guard let appDelegate = NSApplication.shared.delegate as? AppDelegate else {
            logWarning("CrashHandler", "Не удалось получить AppDelegate")
            return
        }
        
        var stateData: [String: Any] = [:]
        
        // Состояние таймера
        if let timer = appDelegate.pomodoroTimer {
            stateData["timer_state"] = timer.state.rawValue
            stateData["time_remaining"] = timer.timeRemaining
            stateData["overtime_elapsed"] = timer.overtimeElapsed
            stateData["completed_intervals"] = timer.completedIntervals
        }
        
        // Состояние окон
        stateData["notification_window_open"] = appDelegate.notificationWindow != nil
        stateData["completion_window_open"] = appDelegate.modernCompletionWindow != nil
        stateData["settings_window_open"] = appDelegate.settingsWindow != nil
        stateData["statistics_window_open"] = appDelegate.statisticsWindow != nil
        stateData["projects_window_open"] = appDelegate.projectsWindow != nil
        
        // Состояние проекта
        if let projectId = appDelegate.currentProjectId {
            stateData["current_project_id"] = projectId.uuidString
        }
        
        Logger.shared.logWithData(.info, "State", "📱 Состояние", data: stateData)
    }
    
    /// Периодическое логирование состояния
    func startPeriodicStateLogging() {
        Timer.scheduledTimer(withTimeInterval: 600.0, repeats: true) { [weak self] _ in
            self?.logApplicationState()
        }

        logInfo("Monitor", "📊 Мониторинг запущен (каждые 10 мин)")
    }
}

/// Расширение для мониторинга производительности
extension CrashHandler {
    
    /// Мониторинг использования CPU
    func logCPUUsage() {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            let cpuData: [String: Any] = [
                "user_time_seconds": Double(info.user_time.seconds) + Double(info.user_time.microseconds) / 1_000_000.0,
                "system_time_seconds": Double(info.system_time.seconds) + Double(info.system_time.microseconds) / 1_000_000.0
            ]
            
            Logger.shared.logWithData(.debug, "Performance", "Использование CPU", data: cpuData)
        }
    }
    
    /// Мониторинг количества потоков
    func logThreadCount() {
        var threadCount: mach_msg_type_number_t = 0
        var threadList: thread_act_array_t?
        
        let result = task_threads(mach_task_self_, &threadList, &threadCount)
        
        if result == KERN_SUCCESS {
            logInfo("Performance", "Количество потоков: \(threadCount)")
            
            // Освобождаем память
            if let list = threadList {
                vm_deallocate(mach_task_self_, vm_address_t(bitPattern: list), vm_size_t(threadCount * UInt32(MemoryLayout<thread_t>.size)))
            }
        }
    }
    
    /// Комплексный мониторинг производительности
    func startPerformanceMonitoring() {
        Timer.scheduledTimer(withTimeInterval: 300.0, repeats: true) { [weak self] _ in
            self?.logCPUUsage()
            self?.logThreadCount()
        }
        
        logInfo("Perf", "⚡ Мониторинг производительности (каждые 5 мин)")
    }

    /// Получить название сигнала
    private func getSignalName(_ signal: Int32) -> String {
        switch signal {
        case SIGABRT: return "SIGABRT"
        case SIGSEGV: return "SIGSEGV"
        case SIGBUS: return "SIGBUS"
        case SIGILL: return "SIGILL"
        case SIGFPE: return "SIGFPE"
        case SIGPIPE: return "SIGPIPE"
        default: return "SIGNAL_\(signal)"
        }
    }
}
