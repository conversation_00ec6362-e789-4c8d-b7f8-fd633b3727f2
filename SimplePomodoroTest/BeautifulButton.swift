import Cocoa

/// Красивая кнопка с градиентом, которая НЕ УЕЗЖАЕТ
class BeautifulButton: NSButton {
    
    private var gradientLayer: CAGradientLayer?
    
    init(title: String, isGreen: Bool, isSmall: Bool) {
        super.init(frame: .zero)
        
        // КРИТИЧЕСКИ ВАЖНО
        self.translatesAutoresizingMaskIntoConstraints = false
        
        self.title = title
        self.isBordered = false
        self.wantsLayer = true
        
        // Настройка размера
        if isSmall {
            self.font = NSFont.systemFont(ofSize: 11, weight: .medium)
        } else {
            self.font = NSFont.systemFont(ofSize: 13, weight: .semibold)
        }
        
        setupGradient(isGreen: isGreen)
        setupText()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupGradient(isGreen: Bool) {
        gradientLayer = CAGradientLayer()
        
        if isGreen {
            // Зеленый градиент для кнопки "Завершить"
            let color1 = NSColor(red: 0.2, green: 0.8, blue: 0.2, alpha: 1.0)
            let color2 = NSColor(red: 0.1, green: 0.6, blue: 0.1, alpha: 1.0)
            gradientLayer?.colors = [color1.cgColor, color2.cgColor]
        } else {
            // Серый градиент для кнопок "+1 мин", "+5 мин"
            let color1 = NSColor(red: 0.45, green: 0.45, blue: 0.5, alpha: 1.0)
            let color2 = NSColor(red: 0.35, green: 0.35, blue: 0.4, alpha: 1.0)
            gradientLayer?.colors = [color1.cgColor, color2.cgColor]
        }
        
        gradientLayer?.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer?.endPoint = CGPoint(x: 0, y: 1)
        gradientLayer?.cornerRadius = 6
        
        // Тень
        gradientLayer?.shadowColor = NSColor.black.cgColor
        gradientLayer?.shadowOpacity = 0.3
        gradientLayer?.shadowOffset = CGSize(width: 0, height: 1)
        gradientLayer?.shadowRadius = 2
        
        if let gradientLayer = gradientLayer {
            self.layer = gradientLayer
            // Устанавливаем фиксированный размер градиента
            DispatchQueue.main.async {
                gradientLayer.frame = self.bounds
            }
        }
    }
    
    private func setupText() {
        // Белый текст
        self.attributedTitle = NSAttributedString(
            string: self.title,
            attributes: [
                .foregroundColor: NSColor.white,
                .font: self.font ?? NSFont.systemFont(ofSize: 13)
            ]
        )
    }
    
    override func layout() {
        super.layout()
        // УБИРАЕМ ОБНОВЛЕНИЕ gradientLayer.frame - ЭТО ВЫЗЫВАЛО "УЕЗЖАНИЕ"!
        // gradientLayer?.frame = bounds
    }
    
    // НЕ ПЕРЕОПРЕДЕЛЯЕМ mouseDown, mouseUp, mouseEntered, mouseExited
    // НЕ ДОБАВЛЯЕМ tracking areas
    // НЕ ДЕЛАЕМ НИЧЕГО, ЧТО МОЖЕТ ПОВЛИЯТЬ НА LAYOUT
}
