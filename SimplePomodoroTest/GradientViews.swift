import Cocoa

// MARK: - Градиентный фон для окна
class GradientView: NSView {
    
    override func awakeFromNib() {
        super.awakeFromNib()
        setupGradient()
    }
    
    override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        setupGradient()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupGradient()
    }
    
    private func setupGradient() {
        self.wantsLayer = true

        // Создаем glassmorphism эффект
        let gradientLayer = CAGradientLayer()

        // Glassmorphism цвета - более темные и сдержанные
        let color1 = NSColor(red: 0.05, green: 0.05, blue: 0.1, alpha: 0.85) // Очень темно-синий
        let color2 = NSColor(red: 0.08, green: 0.05, blue: 0.12, alpha: 0.8) // Темно-фиолетовый
        let color3 = NSColor(red: 0.05, green: 0.08, blue: 0.12, alpha: 0.85) // Темно-серо-синий

        gradientLayer.colors = [color1.cgColor, color2.cgColor, color3.cgColor]
        gradientLayer.locations = [0.0, 0.5, 1.0]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 1)
        gradientLayer.cornerRadius = 12

        // Темная граница для элегантности
        gradientLayer.borderColor = NSColor(white: 0.15, alpha: 0.6).cgColor
        gradientLayer.borderWidth = 1

        self.layer = gradientLayer

        // Добавляем размытие фона (backdrop filter эффект)
        if let visualEffectView = self.superview as? NSVisualEffectView {
            visualEffectView.material = .hudWindow
            visualEffectView.blendingMode = .behindWindow
            visualEffectView.state = .active
        }

        // Тень для глубины
        self.layer?.shadowColor = NSColor.black.cgColor
        self.layer?.shadowOpacity = 0.3
        self.layer?.shadowOffset = CGSize(width: 0, height: 4)
        self.layer?.shadowRadius = 20
    }
    
    override func layout() {
        super.layout()
        // Обновляем размер градиента при изменении размера view
        self.layer?.frame = self.bounds
    }
}

// MARK: - Градиентные кнопки
class GradientButton: NSButton {
    
    private let isPrimary: Bool
    private var gradientLayer: CAGradientLayer?
    
    init(title: String, isPrimary: Bool) {
        self.isPrimary = isPrimary
        super.init(frame: .zero)
        
        self.title = title
        self.isBordered = false
        self.wantsLayer = true
        
        setupAppearance()
        setupTracking()
    }
    
    required init?(coder: NSCoder) {
        self.isPrimary = false
        super.init(coder: coder)
        setupAppearance()
        setupTracking()
    }
    
    private func setupAppearance() {
        // Настройка текста (уменьшили размер)
        self.font = NSFont.systemFont(ofSize: 10, weight: .medium)

        // Белый текст для обеих кнопок
        self.attributedTitle = NSAttributedString(
            string: self.title,
            attributes: [
                .foregroundColor: NSColor.white,
                .font: NSFont.systemFont(ofSize: 10, weight: isPrimary ? .semibold : .medium)
            ]
        )

        setupGradient()
    }
    
    private func setupGradient() {
        gradientLayer = CAGradientLayer()

        if isPrimary {
            // Основная кнопка - современный синий градиент
            let color1 = NSColor(red: 0.0, green: 0.48, blue: 1.0, alpha: 1.0) // iOS синий
            let color2 = NSColor(red: 0.0, green: 0.38, blue: 0.85, alpha: 1.0) // Темнее
            gradientLayer?.colors = [color1.cgColor, color2.cgColor]
        } else {
            // Вторичная кнопка - современный серый градиент
            let color1 = NSColor(red: 0.45, green: 0.45, blue: 0.5, alpha: 1.0)
            let color2 = NSColor(red: 0.35, green: 0.35, blue: 0.4, alpha: 1.0)
            gradientLayer?.colors = [color1.cgColor, color2.cgColor]
        }

        gradientLayer?.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer?.endPoint = CGPoint(x: 0, y: 1)
        gradientLayer?.cornerRadius = 12 // Компактное скругление

        // Тонкая граница для элегантности
        if isPrimary {
            gradientLayer?.borderColor = NSColor(red: 0.0, green: 0.3, blue: 0.7, alpha: 0.8).cgColor
        } else {
            gradientLayer?.borderColor = NSColor(white: 0.25, alpha: 0.6).cgColor
        }
        gradientLayer?.borderWidth = 0.5

        // Добавляем внутреннюю тень для глубины
        gradientLayer?.shadowColor = NSColor.black.cgColor
        gradientLayer?.shadowOpacity = 0.2
        gradientLayer?.shadowOffset = CGSize(width: 0, height: 1)
        gradientLayer?.shadowRadius = 2

        self.layer = gradientLayer
    }
    
    private func setupTracking() {
        // Добавляем отслеживание мыши для hover эффектов
        let trackingArea = NSTrackingArea(
            rect: self.bounds,
            options: [.mouseEnteredAndExited, .activeInKeyWindow, .inVisibleRect],
            owner: self,
            userInfo: nil
        )
        self.addTrackingArea(trackingArea)
    }
    
    override func mouseEntered(with event: NSEvent) {
        super.mouseEntered(with: event)

        // Современная анимация при наведении
        NSAnimationContext.runAnimationGroup { context in
            context.duration = 0.15
            context.timingFunction = CAMediaTimingFunction(name: .easeOut)

            // Легкое увеличение и изменение яркости
            let transform = CATransform3DMakeScale(1.02, 1.02, 1)
            self.layer?.transform = transform

            // Увеличиваем яркость градиента
            if isPrimary {
                let color1 = NSColor(red: 0.1, green: 0.58, blue: 1.0, alpha: 1.0)
                let color2 = NSColor(red: 0.1, green: 0.48, blue: 0.95, alpha: 1.0)
                gradientLayer?.colors = [color1.cgColor, color2.cgColor]
            } else {
                let color1 = NSColor(red: 0.55, green: 0.55, blue: 0.6, alpha: 1.0)
                let color2 = NSColor(red: 0.45, green: 0.45, blue: 0.5, alpha: 1.0)
                gradientLayer?.colors = [color1.cgColor, color2.cgColor]
            }
        }
    }

    override func mouseExited(with event: NSEvent) {
        super.mouseExited(with: event)

        // Возвращаем к исходному состоянию
        NSAnimationContext.runAnimationGroup { context in
            context.duration = 0.15
            context.timingFunction = CAMediaTimingFunction(name: .easeOut)

            self.layer?.transform = CATransform3DIdentity

            // Возвращаем исходные цвета
            if isPrimary {
                let color1 = NSColor(red: 0.0, green: 0.48, blue: 1.0, alpha: 1.0)
                let color2 = NSColor(red: 0.0, green: 0.38, blue: 0.85, alpha: 1.0)
                gradientLayer?.colors = [color1.cgColor, color2.cgColor]
            } else {
                let color1 = NSColor(red: 0.45, green: 0.45, blue: 0.5, alpha: 1.0)
                let color2 = NSColor(red: 0.35, green: 0.35, blue: 0.4, alpha: 1.0)
                gradientLayer?.colors = [color1.cgColor, color2.cgColor]
            }
        }
    }
    
    override func mouseDown(with event: NSEvent) {
        super.mouseDown(with: event)

        // Легкая анимация нажатия без изменения позиции
        NSAnimationContext.runAnimationGroup { context in
            context.duration = 0.05
            self.animator().alphaValue = 0.8
        }
    }

    override func mouseUp(with event: NSEvent) {
        super.mouseUp(with: event)

        // Возвращаем прозрачность
        NSAnimationContext.runAnimationGroup { context in
            context.duration = 0.1
            self.animator().alphaValue = 1.0
        }
    }
    
    override func layout() {
        super.layout()
        gradientLayer?.frame = self.bounds
        
        // Обновляем tracking area
        self.trackingAreas.forEach { self.removeTrackingArea($0) }
        let trackingArea = NSTrackingArea(
            rect: self.bounds,
            options: [.mouseEnteredAndExited, .activeInKeyWindow, .inVisibleRect],
            owner: self,
            userInfo: nil
        )
        self.addTrackingArea(trackingArea)
    }
}
