import Cocoa

/// Окно уведомления о начале отдыха с таймером и напоминанием
class BreakStartWindow: NSWindow {
    
    // Колбэки для действий
    var onSkipBreak: (() -> Void)?
    var onStartBreak: (() -> Void)?
    var onHideForBreak: (() -> Void)?
    
    // UI элементы
    private var timeLabel: CustomTimeView!
    private var activityWarningLabel: NSTextField!
    private var timer: Timer?
    private var timeRemaining: TimeInterval = 15 * 60 // 15 минут
    
    override init(contentRect: NSRect, styleMask style: NSWindow.StyleMask, backing backingStoreType: NSWindow.BackingStoreType, defer flag: Bool) {
        super.init(contentRect: NSRect(x: 0, y: 0, width: 420, height: 170), // Уменьшили ширину до 370px
                  styleMask: [.borderless],
                  backing: .buffered,
                  defer: false)

        setupWindow()
        setupUI()
    }
    
    private func setupWindow() {
        print("🌿 BreakStartWindow: Настройка окна начала отдыха")

        // Настройка окна
        self.isOpaque = false
        self.backgroundColor = NSColor.clear
        self.level = .floating
        self.hasShadow = true
        self.isMovableByWindowBackground = true

        // Убеждаемся, что окно может получать события
        self.acceptsMouseMovedEvents = true
        self.ignoresMouseEvents = false
        print("🌿 BreakStartWindow: Окно настроено для получения событий мыши")
    }

    func positionRelativeToStatusItem(statusItemFrame: NSRect) {
        print("🌿 BreakStartWindow: Позиционирование возле status item")

        let windowWidth = self.frame.width
        let windowHeight = self.frame.height

        // Позиционируем под status item с небольшим отступом
        let x = statusItemFrame.midX - windowWidth / 2
        let y = statusItemFrame.minY - windowHeight - 8

        self.setFrameOrigin(NSPoint(x: x, y: y))
    }
    
    private func setupUI() {
        print("🌿 BreakStartWindow: Создание UI для начала отдыха - двухколоночный layout")

        // Создаем основной контейнер
        let containerView = NSView()
        containerView.wantsLayer = true
        containerView.translatesAutoresizingMaskIntoConstraints = false

        // Создаем сложный фон с несколькими радиальными градиентами
        setupComplexBackground(for: containerView)

        // Создаем верхнюю область (текст + таймер) и нижнюю область (кнопки)
        let topArea = NSView()
        topArea.translatesAutoresizingMaskIntoConstraints = false

        let bottomArea = NSView()
        bottomArea.translatesAutoresizingMaskIntoConstraints = false

        // Верхняя область: две колонки для текста и таймера
        let leftColumn = NSView()
        leftColumn.translatesAutoresizingMaskIntoConstraints = false

        let rightColumn = NSView()
        rightColumn.translatesAutoresizingMaskIntoConstraints = false

        // Нижняя область: две колонки для кнопок
        let leftButtonArea = NSView()
        leftButtonArea.translatesAutoresizingMaskIntoConstraints = false

        let rightButtonArea = NSView()
        rightButtonArea.translatesAutoresizingMaskIntoConstraints = false

        // ЛЕВАЯ КОЛОНКА - Текстовая информация и кнопка действия

        // Предупреждение об активности
        activityWarningLabel = NSTextField(labelWithString: "⚠️ Обнаружена активность")
        activityWarningLabel.font = NSFont.systemFont(ofSize: 11, weight: .medium)
        activityWarningLabel.textColor = NSColor.yellow
        activityWarningLabel.alignment = .left
        activityWarningLabel.translatesAutoresizingMaskIntoConstraints = false
        activityWarningLabel.isHidden = false

        // Заголовок с иконкой
        let titleLabel = NSTextField(labelWithString: "🍃 Время отдыха!")
        titleLabel.font = NSFont.systemFont(ofSize: 20, weight: .bold)
        titleLabel.textColor = NSColor.white
        titleLabel.alignment = .left
        titleLabel.translatesAutoresizingMaskIntoConstraints = false

        // Подзаголовок с инструкциями
        let subtitleLabel = NSTextField(labelWithString: "Закройте ноутбук и отдохните")
        subtitleLabel.font = NSFont.systemFont(ofSize: 14, weight: .medium)
        subtitleLabel.textColor = NSColor.white.withAlphaComponent(0.9)
        subtitleLabel.alignment = .left
        subtitleLabel.translatesAutoresizingMaskIntoConstraints = false

        // Кнопка "Иду отдыхать" в левой колонке
        let okButton = createButton(title: "Иду отдыхать", color: .white, isSmall: false)
        okButton.target = self
        okButton.action = #selector(startBreakClicked)
        okButton.isEnabled = true
        print("🌿 BreakStartWindow: Кнопка 'Иду отдыхать' создана и настроена")

        // ПРАВАЯ КОЛОНКА - Таймер и кнопка скрытия

        // Таймер
        timeLabel = CustomTimeView()
        timeLabel.translatesAutoresizingMaskIntoConstraints = false
        timeLabel.updateTime(formatTime(timeRemaining))

        // Кнопка "Я отдыхаю за компом" в правой колонке (красная, потому что это неправильно)
        let hideButton = createButton(title: "Я отдыхаю за компом", color: NSColor.red, isSmall: false)
        hideButton.target = self
        hideButton.action = #selector(hideForBreakClicked)
        hideButton.isEnabled = true
        print("🌿 BreakStartWindow: Кнопка 'Я отдыхаю за компом' создана и настроена")

        // Добавляем элементы в верхние колонки (без кнопок)
        leftColumn.addSubview(activityWarningLabel)
        leftColumn.addSubview(titleLabel)
        leftColumn.addSubview(subtitleLabel)

        rightColumn.addSubview(timeLabel)

        // Добавляем кнопки в нижние области
        leftButtonArea.addSubview(okButton)
        rightButtonArea.addSubview(hideButton)

        // Добавляем колонки в верхнюю область
        topArea.addSubview(leftColumn)
        topArea.addSubview(rightColumn)

        // Добавляем кнопочные области в нижнюю область
        bottomArea.addSubview(leftButtonArea)
        bottomArea.addSubview(rightButtonArea)

        // Добавляем области в контейнер
        containerView.addSubview(topArea)
        containerView.addSubview(bottomArea)

        self.contentView = containerView

        // Настройка constraints для новой структуры: верх (текст+таймер) + низ (кнопки)
        NSLayoutConstraint.activate([
            // Верхняя область (60% высоты)
            topArea.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 16),
            topArea.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            topArea.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),
            topArea.heightAnchor.constraint(equalTo: containerView.heightAnchor, multiplier: 0.6),

            // Нижняя область (40% высоты)
            bottomArea.topAnchor.constraint(equalTo: topArea.bottomAnchor, constant: 8),
            bottomArea.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            bottomArea.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),
            bottomArea.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -16),

            // Верхние колонки (текст слева 60%, таймер справа 40%)
            leftColumn.topAnchor.constraint(equalTo: topArea.topAnchor),
            leftColumn.leadingAnchor.constraint(equalTo: topArea.leadingAnchor),
            leftColumn.bottomAnchor.constraint(equalTo: topArea.bottomAnchor),
            leftColumn.widthAnchor.constraint(equalTo: topArea.widthAnchor, multiplier: 0.60), // 60% для текста

            rightColumn.topAnchor.constraint(equalTo: topArea.topAnchor),
            rightColumn.trailingAnchor.constraint(equalTo: topArea.trailingAnchor),
            rightColumn.bottomAnchor.constraint(equalTo: topArea.bottomAnchor),
            rightColumn.leadingAnchor.constraint(equalTo: leftColumn.trailingAnchor, constant: 16),

            // Элементы левой колонки
            activityWarningLabel.topAnchor.constraint(equalTo: leftColumn.topAnchor),
            activityWarningLabel.leadingAnchor.constraint(equalTo: leftColumn.leadingAnchor),
            activityWarningLabel.trailingAnchor.constraint(equalTo: leftColumn.trailingAnchor),

            titleLabel.topAnchor.constraint(equalTo: activityWarningLabel.bottomAnchor, constant: 12),
            titleLabel.leadingAnchor.constraint(equalTo: leftColumn.leadingAnchor),
            titleLabel.trailingAnchor.constraint(equalTo: leftColumn.trailingAnchor),

            subtitleLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 6),
            subtitleLabel.leadingAnchor.constraint(equalTo: leftColumn.leadingAnchor),
            subtitleLabel.trailingAnchor.constraint(equalTo: leftColumn.trailingAnchor),

            // Элементы правой колонки (только таймер)
            timeLabel.centerYAnchor.constraint(equalTo: rightColumn.centerYAnchor, constant: -10), // Приподнимаем таймер
            timeLabel.leadingAnchor.constraint(equalTo: rightColumn.leadingAnchor, constant: 5),
            timeLabel.trailingAnchor.constraint(equalTo: rightColumn.trailingAnchor, constant: -10), // Уменьшили отступ справа
            timeLabel.heightAnchor.constraint(equalToConstant: 60),

            // Нижние кнопочные области (левая 40%, правая 60%)
            leftButtonArea.topAnchor.constraint(equalTo: bottomArea.topAnchor),
            leftButtonArea.leadingAnchor.constraint(equalTo: bottomArea.leadingAnchor),
            leftButtonArea.bottomAnchor.constraint(equalTo: bottomArea.bottomAnchor),
            leftButtonArea.widthAnchor.constraint(equalTo: bottomArea.widthAnchor, multiplier: 0.40), // 40% для левой кнопки

            rightButtonArea.topAnchor.constraint(equalTo: bottomArea.topAnchor),
            rightButtonArea.trailingAnchor.constraint(equalTo: bottomArea.trailingAnchor),
            rightButtonArea.bottomAnchor.constraint(equalTo: bottomArea.bottomAnchor),
            rightButtonArea.leadingAnchor.constraint(equalTo: leftButtonArea.trailingAnchor, constant: 16),

            // Кнопки в своих областях
            okButton.centerYAnchor.constraint(equalTo: leftButtonArea.centerYAnchor),
            okButton.leadingAnchor.constraint(equalTo: leftButtonArea.leadingAnchor),
            okButton.trailingAnchor.constraint(equalTo: leftButtonArea.trailingAnchor),
            okButton.heightAnchor.constraint(equalToConstant: 38),

            hideButton.centerYAnchor.constraint(equalTo: rightButtonArea.centerYAnchor),
            hideButton.leadingAnchor.constraint(equalTo: rightButtonArea.leadingAnchor),
            hideButton.trailingAnchor.constraint(equalTo: rightButtonArea.trailingAnchor),
            hideButton.heightAnchor.constraint(equalToConstant: 38),
        ])

        // Обновляем размеры всех градиентов при изменении размера
        DispatchQueue.main.async {
            if let mainLayer = containerView.layer {
                mainLayer.frame = containerView.bounds

                // Обновляем размеры всех радиальных градиентов
                for sublayer in mainLayer.sublayers ?? [] {
                    if let gradientLayer = sublayer as? CAGradientLayer {
                        gradientLayer.frame = containerView.bounds
                    }
                }
            }

            // Обновляем размеры градиентов кнопок
            if let okButtonLayer = okButton.layer {
                okButtonLayer.frame = okButton.bounds
            }
            if let hideButtonLayer = hideButton.layer {
                hideButtonLayer.frame = hideButton.bounds
            }
        }
    }

    private func setupComplexBackground(for view: NSView) {
        // Более насыщенный зеленый градиентный фон для окна отдыха
        let mainGradient = CAGradientLayer()
        mainGradient.colors = [
            NSColor(red: 0.15, green: 0.5, blue: 0.35, alpha: 0.96).cgColor,  // Темно-зеленый
            NSColor(red: 0.2, green: 0.6, blue: 0.4, alpha: 0.96).cgColor,    // Средний зеленый
            NSColor(red: 0.25, green: 0.65, blue: 0.45, alpha: 0.96).cgColor  // Светло-зеленый
        ]
        mainGradient.startPoint = CGPoint(x: 0, y: 0)
        mainGradient.endPoint = CGPoint(x: 1, y: 1)
        mainGradient.cornerRadius = 12

        // Тень
        mainGradient.shadowColor = NSColor.black.cgColor
        mainGradient.shadowOpacity = 0.3
        mainGradient.shadowOffset = CGSize(width: 0, height: 4)
        mainGradient.shadowRadius = 12

        view.layer = mainGradient

        // Добавляем радиальные градиенты для красоты
        DispatchQueue.main.async {
            self.addRadialGradients(to: view)
        }
    }

    private func addRadialGradients(to view: NSView) {
        // Зеленые радиальные градиенты для окна отдыха
        guard let layer = view.layer else { return }

        // Первый радиальный градиент (левый верхний) - светло-зеленый
        let radial1 = CAGradientLayer()
        radial1.type = .radial
        radial1.colors = [
            NSColor(red: 0.6, green: 0.9, blue: 0.7, alpha: 0.4).cgColor,  // Светло-зеленый
            NSColor.clear.cgColor
        ]
        radial1.startPoint = CGPoint(x: 0.2, y: 0.8)
        radial1.endPoint = CGPoint(x: 0.8, y: 0.2)
        radial1.frame = layer.bounds
        layer.addSublayer(radial1)

        // Второй радиальный градиент (правый нижний) - желтовато-зеленый
        let radial2 = CAGradientLayer()
        radial2.type = .radial
        radial2.colors = [
            NSColor(red: 0.7, green: 0.8, blue: 0.4, alpha: 0.3).cgColor,  // Желтовато-зеленый
            NSColor.clear.cgColor
        ]
        radial2.startPoint = CGPoint(x: 0.8, y: 0.2)
        radial2.endPoint = CGPoint(x: 0.2, y: 0.8)
        radial2.frame = layer.bounds
        layer.addSublayer(radial2)
    }

    private func createButton(title: String, color: NSColor, isSmall: Bool) -> NSButton {
        let button = NSButton()
        button.title = ""
        button.isBordered = false
        button.wantsLayer = true
        button.translatesAutoresizingMaskIntoConstraints = false

        // Создаем более красивый градиентный фон для кнопки
        let gradientLayer = CAGradientLayer()
        if color == .white {
            gradientLayer.colors = [
                NSColor.white.withAlphaComponent(0.95).cgColor,
                NSColor.white.withAlphaComponent(0.85).cgColor
            ]

            // Добавляем тень для белой кнопки
            gradientLayer.shadowColor = NSColor.black.cgColor
            gradientLayer.shadowOffset = CGSize(width: 0, height: 2)
            gradientLayer.shadowOpacity = 0.15
            gradientLayer.shadowRadius = 4
        } else {
            gradientLayer.colors = [
                color.withAlphaComponent(0.8).cgColor,
                color.withAlphaComponent(0.6).cgColor
            ]

            // Добавляем тень для цветной кнопки
            gradientLayer.shadowColor = NSColor.black.cgColor
            gradientLayer.shadowOffset = CGSize(width: 0, height: 1)
            gradientLayer.shadowOpacity = 0.2
            gradientLayer.shadowRadius = 3
        }

        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 0, y: 1)
        gradientLayer.cornerRadius = isSmall ? 8 : 10 // Увеличили радиус скругления
        button.layer = gradientLayer

        // Настройка текста с улучшенным шрифтом
        let textColor = color == .white ? NSColor.black : NSColor.white

        // Создаем параграф стиль для центрирования текста
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.alignment = .center

        button.attributedTitle = NSAttributedString(
            string: title,
            attributes: [
                .foregroundColor: textColor,
                .font: NSFont.systemFont(ofSize: isSmall ? 12 : 14, weight: .semibold),
                .paragraphStyle: paragraphStyle
            ]
        )

        // Настройка выравнивания кнопки
        button.alignment = .center

        // Обновляем размер градиента после установки constraints
        DispatchQueue.main.async {
            gradientLayer.frame = button.bounds
        }

        return button
    }

    // MARK: - Управление таймером

    func startTimer() {
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateTimer()
        }
    }

    func stopTimer() {
        timer?.invalidate()
        timer = nil
    }

    private func updateTimer() {
        timeRemaining -= 1
        updateTimeRemaining(timeRemaining) // Используем наш новый метод с правильным окрашиванием

        if timeRemaining <= 0 {
            stopTimer()
            startBreakClicked()
        }
    }

    func updateTimeRemaining(_ time: TimeInterval) {
        timeRemaining = time
        let timeText = formatTime(time)
        timeLabel.updateTime(timeText)
    }

    func showActivityWarning() {
        activityWarningLabel.isHidden = false
    }

    func hideActivityWarning() {
        activityWarningLabel.isHidden = true
    }

    private func formatTime(_ time: TimeInterval) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }

    // MARK: - Actions

    @objc private func skipBreakClicked() {
        print("🌿 BreakStartWindow: Пропустить отдых")
        stopTimer()
        hideWithAnimation {
            self.onSkipBreak?()
        }
    }

    @objc private func startBreakClicked() {
        print("🌿 BreakStartWindow: Начать отдых")
        stopTimer()
        hideWithAnimation {
            self.onStartBreak?()
        }
    }

    @objc private func hideForBreakClicked() {
        print("🌿 BreakStartWindow: Я отдыхаю за компом")
        stopTimer()
        hideWithAnimation {
            self.onHideForBreak?()
        }
    }

    // MARK: - Анимации

    func showWithAnimation() {
        self.alphaValue = 0.0
        self.makeKeyAndOrderFront(nil)

        // Запускаем таймер при показе окна
        startTimer()

        NSAnimationContext.runAnimationGroup({ context in
            context.duration = 0.3
            context.timingFunction = CAMediaTimingFunction(name: .easeOut)
            self.animator().alphaValue = 1.0
        })
    }

    func hideWithAnimation(completion: @escaping () -> Void) {
        NSAnimationContext.runAnimationGroup({ context in
            context.duration = 0.2
            context.timingFunction = CAMediaTimingFunction(name: .easeIn)
            self.animator().alphaValue = 0.0
        }, completionHandler: {
            self.orderOut(nil)
            completion()
        })
    }
}

// MARK: - Кастомный view для отображения времени с гарантированным зеленым цветом
class CustomTimeView: NSView {
    private var timeText: String = "15:00"
    private let greenColor = NSColor.white // Белый цвет для лучшей читаемости
    private let font = NSFont.monospacedDigitSystemFont(ofSize: 50, weight: .bold) // Увеличили размер на 7% (60 * 1.07 = 64)

    override func draw(_ dirtyRect: NSRect) {
        super.draw(dirtyRect)

        // Очищаем фон
        NSColor.clear.setFill()
        dirtyRect.fill()

        // Создаем тень для текста
        let shadow = NSShadow()
        shadow.shadowColor = NSColor.black.withAlphaComponent(0.3)
        shadow.shadowOffset = NSSize(width: 0, height: -2)
        shadow.shadowBlurRadius = 4

        // Рисуем текст белым цветом с тенью
        let attributes: [NSAttributedString.Key: Any] = [
            .foregroundColor: greenColor,
            .font: font,
            .shadow: shadow
        ]

        let attributedString = NSAttributedString(string: timeText, attributes: attributes)
        let textSize = attributedString.size()

        // Центрируем текст
        let textRect = NSRect(
            x: (bounds.width - textSize.width) / 2,
            y: (bounds.height - textSize.height) / 2,
            width: textSize.width,
            height: textSize.height
        )

        attributedString.draw(in: textRect)
    }

    func updateTime(_ time: String) {
        timeText = time
        needsDisplay = true // Перерисовываем view
    }
}
