import Foundation

// MARK: - Модели данных для анализа

struct WorkPattern {
    let averageIntervalsPerDay: Double
    let workingDaysPerWeek: Int
    let consistencyScore: Double // 0-1, где 1 = очень стабильно
    let averageStartTime: TimeInterval? // секунды с начала дня
    let averageIntervalDuration: TimeInterval // средняя продолжительность интервала в секундах
    let riskFactors: [RiskFactor]
    let recommendations: [Recommendation]
}

enum RiskFactor {
    case overwork(averagePerDay: Double)
    case longIntervals(averageDuration: TimeInterval)
    case inconsistency(variance: Double)
    case lateStart(averageHour: Int)
    case burnoutRisk(intenseDays: Int, restDays: Int)
    case procrastination(gapDays: Int)
}

enum Recommendation {
    case reduceWorkload(currentAverage: Double, suggested: Double)
    case increaseConsistency(suggestion: String)
    case startEarlier(currentHour: Int, suggestedHour: Int)
    case takeBreaks(reason: String)
    case establishRoutine(suggestion: String)
    case maintainBalance(praise: String)
}

// MARK: - Структуры данных для графиков

struct DayChartData {
    let dayName: String
    let normalIntervals: Int
    let overtimeIntervals: Int
    let totalIntervals: Int

    init(dayName: String, normalIntervals: Int, overtimeIntervals: Int) {
        self.dayName = dayName
        self.normalIntervals = normalIntervals
        self.overtimeIntervals = overtimeIntervals
        self.totalIntervals = normalIntervals + overtimeIntervals
    }
}

// MARK: - Анализатор рабочих паттернов

class WorkPatternAnalyzer {
    let statisticsManager: StatisticsManager
    
    init(statisticsManager: StatisticsManager) {
        self.statisticsManager = statisticsManager
    }
    
    // MARK: - Основной анализ
    
    func analyzeWorkPattern(for period: AnalysisPeriod = .lastMonth) -> WorkPattern {
        print("🔍 Начинаем анализ паттерна для периода: \(period)")

        let intervals = getIntervalsForPeriod(period)
        print("🔍 Получено интервалов: \(intervals.count)")

        // Получаем даты начала и конца периода для правильного расчета статистики
        let (startDate, endDate) = getPeriodDates(period)

        if intervals.isEmpty {
            print("❌ Нет интервалов для анализа! Возвращаем пустой паттерн.")
            return WorkPattern(
                averageIntervalsPerDay: 0.0,
                workingDaysPerWeek: 0,
                consistencyScore: 0.0,
                averageStartTime: nil,
                averageIntervalDuration: 0.0,
                riskFactors: [],
                recommendations: [.establishRoutine(suggestion: "Начните работать регулярно - хотя бы по 1-2 интервала в день")]
            )
        } else {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "dd.MM HH:mm"
            print("🔍 Первый интервал: \(dateFormatter.string(from: intervals.first!.0))")
            print("🔍 Последний интервал: \(dateFormatter.string(from: intervals.last!.0))")
        }

        let dailyStats = calculateDailyStatistics(intervals, startDate: startDate, endDate: endDate)
        print("🔍 Статистика по дням: \(dailyStats)")

        let averageIntervalsPerDay = calculateAverageIntervalsPerDay(dailyStats)
        print("🔍 Среднее интервалов в день: \(averageIntervalsPerDay)")

        let workingDaysPerWeek = calculateWorkingDaysPerWeek(dailyStats)
        print("🔍 Рабочих дней в неделю: \(workingDaysPerWeek)")

        let consistencyScore = calculateConsistencyScore(dailyStats)
        print("🔍 Оценка стабильности: \(consistencyScore)")

        let averageStartTime = calculateAverageStartTime(intervals)
        if let startTime = averageStartTime {
            let hours = Int(startTime / 3600)
            let minutes = Int((startTime.truncatingRemainder(dividingBy: 3600)) / 60)
            print("🔍 Среднее время начала: \(hours):\(String(format: "%02d", minutes))")
        } else {
            print("🔍 Среднее время начала: не определено")
        }

        let averageIntervalDuration = calculateAverageIntervalDuration(intervals)
        print("🔍 Средняя продолжительность интервала: \(Int(averageIntervalDuration/60)) мин")

        let overtimeIntervals = intervals.filter { $0.1 > 3600 } // Интервалы длиннее 60 минут
        print("🔍 Интервалов с переработкой (>60 мин): \(overtimeIntervals.count) из \(intervals.count)")
        
        let riskFactors = identifyRiskFactors(
            intervals: intervals,
            dailyStats: dailyStats,
            averageIntervalsPerDay: averageIntervalsPerDay,
            averageStartTime: averageStartTime,
            workingDaysPerWeek: workingDaysPerWeek,
            consistencyScore: consistencyScore,
            averageIntervalDuration: averageIntervalDuration
        )
        print("🔍 Выявлено рисков: \(riskFactors.count)")
        for risk in riskFactors {
            print("   ⚠️ \(risk)")
        }
        
        let recommendations = generateRecommendations(
            riskFactors: riskFactors,
            averageIntervalsPerDay: averageIntervalsPerDay,
            workingDaysPerWeek: workingDaysPerWeek,
            consistencyScore: consistencyScore,
            averageStartTime: averageStartTime
        )
        
        return WorkPattern(
            averageIntervalsPerDay: averageIntervalsPerDay,
            workingDaysPerWeek: workingDaysPerWeek,
            consistencyScore: consistencyScore,
            averageStartTime: averageStartTime,
            averageIntervalDuration: averageIntervalDuration,
            riskFactors: riskFactors,
            recommendations: recommendations
        )
    }
    
    // MARK: - Вспомогательные методы
    
    func getIntervalsForPeriod(_ period: AnalysisPeriod) -> [(Date, TimeInterval)] {
        let calendar = Calendar.current
        let now = Date()
        
        let startDate: Date
        let endDate: Date

        switch period {
        case .lastWeek:
            startDate = calendar.date(byAdding: .day, value: -7, to: now) ?? now
            endDate = now
        case .lastMonth:
            startDate = calendar.date(byAdding: .day, value: -30, to: now) ?? now
            endDate = now
        case .lastThreeMonths:
            startDate = calendar.date(byAdding: .day, value: -90, to: now) ?? now
            endDate = now
        case .customWeek(let offset):
            // Получаем начало текущей недели
            guard let currentWeekStart = calendar.dateInterval(of: .weekOfYear, for: now)?.start else {
                startDate = calendar.date(byAdding: .day, value: -7, to: now) ?? now
                endDate = now
                break
            }

            // Смещаем на нужное количество недель
            let targetWeekStart = calendar.date(byAdding: .weekOfYear, value: offset, to: currentWeekStart) ?? currentWeekStart
            let targetWeekEnd = calendar.date(byAdding: .day, value: 7, to: targetWeekStart) ?? targetWeekStart

            startDate = targetWeekStart
            endDate = targetWeekEnd

        case .customRange(let start, let end):
            startDate = start
            endDate = end
        }

        let allIntervals = statisticsManager.getRecentIntervals(limit: 1000)
        let filteredIntervals = allIntervals.filter { $0.0 >= startDate && $0.0 < endDate }

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "dd.MM.yyyy HH:mm"
        print("🔍 getIntervalsForPeriod: период \(dateFormatter.string(from: startDate)) - \(dateFormatter.string(from: endDate))")
        print("🔍 Всего интервалов в базе: \(allIntervals.count)")
        print("🔍 Интервалов в периоде: \(filteredIntervals.count)")

        if !filteredIntervals.isEmpty {
            print("🔍 Первый интервал в периоде: \(dateFormatter.string(from: filteredIntervals.first!.0))")
            print("🔍 Последний интервал в периоде: \(dateFormatter.string(from: filteredIntervals.last!.0))")
        }

        return filteredIntervals
    }

    func getPeriodDates(_ period: AnalysisPeriod) -> (startDate: Date, endDate: Date) {
        let calendar = Calendar.current
        let now = Date()

        switch period {
        case .customWeek(let offset):
            // Получаем начало текущей недели (понедельник)
            let weekday = calendar.component(.weekday, from: now)
            let daysFromMonday = (weekday == 1) ? 6 : weekday - 2
            let startOfCurrentWeek = calendar.date(byAdding: .day, value: -daysFromMonday, to: calendar.startOfDay(for: now))!

            // Смещаем на нужное количество недель
            let startOfTargetWeek = calendar.date(byAdding: .day, value: offset * 7, to: startOfCurrentWeek)!
            let endOfTargetWeek = calendar.date(byAdding: .day, value: 7, to: startOfTargetWeek)!

            return (startOfTargetWeek, endOfTargetWeek)

        case .lastWeek:
            return getPeriodDates(.customWeek(offset: -1))

        case .lastMonth:
            let startOfMonth = calendar.dateInterval(of: .month, for: calendar.date(byAdding: .month, value: -1, to: now)!)!.start
            let endOfMonth = calendar.dateInterval(of: .month, for: calendar.date(byAdding: .month, value: -1, to: now)!)!.end
            return (startOfMonth, endOfMonth)

        case .lastThreeMonths:
            let startDate = calendar.date(byAdding: .month, value: -3, to: now)!
            return (startDate, now)

        case .customRange(let start, let end):
            return (start, end)
        }
    }

    // MARK: - Данные для графиков

    func getWeeklyChartData(for period: AnalysisPeriod) -> [DayChartData] {
        let intervals = getIntervalsForPeriod(period)
        let (startDate, _) = getPeriodDates(period)

        let calendar = Calendar.current
        var chartData: [DayChartData] = []

        // Создаем данные для каждого дня недели
        let dayNames = ["Пн", "Вт", "Ср", "Чт", "Пт", "Сб", "Вс"]

        for dayIndex in 0..<7 {
            let dayDate = calendar.date(byAdding: .day, value: dayIndex, to: startDate)!

            // Фильтруем интервалы для этого дня
            let dayIntervals = intervals.filter { interval in
                calendar.isDate(interval.0, inSameDayAs: dayDate)
            }

            // Разделяем на обычные и переработки (интервалы > 60 минут)
            let normalIntervals = dayIntervals.filter { $0.1 <= 3600 }.count
            let overtimeIntervals = dayIntervals.filter { $0.1 > 3600 }.count

            let dayData = DayChartData(
                dayName: dayNames[dayIndex],
                normalIntervals: normalIntervals,
                overtimeIntervals: overtimeIntervals
            )

            chartData.append(dayData)
        }

        return chartData
    }

    private func calculateDailyStatistics(_ intervals: [(Date, TimeInterval)], startDate: Date, endDate: Date) -> [Date: Int] {
        let calendar = Calendar.current
        var dailyStats: [Date: Int] = [:]

        // Сначала создаем записи для всех дней в периоде с нулевыми значениями
        var currentDate = calendar.startOfDay(for: startDate)
        let endOfPeriod = calendar.startOfDay(for: endDate)

        while currentDate < endOfPeriod {
            dailyStats[currentDate] = 0
            currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate)!
        }

        // Затем добавляем фактические интервалы
        for (date, _) in intervals {
            let day = calendar.startOfDay(for: date)
            if day >= calendar.startOfDay(for: startDate) && day < endOfPeriod {
                dailyStats[day, default: 0] += 1
            }
        }

        return dailyStats
    }
    
    private func calculateAverageIntervalsPerDay(_ dailyStats: [Date: Int]) -> Double {
        guard !dailyStats.isEmpty else { return 0 }

        let calendar = Calendar.current
        let now = Date()
        let today = calendar.startOfDay(for: now)

        // Считаем только прошедшие дни (включая сегодня, если он уже начался)
        let pastDays = dailyStats.keys.filter { $0 <= today }

        // Считаем рабочие дни в прошедшем периоде (пн-пт + сб/вс если была работа)
        var workingDaysCount = 0
        for date in pastDays {
            let weekday = calendar.component(.weekday, from: date)
            let isWeekend = weekday == 1 || weekday == 7 // воскресенье = 1, суббота = 7
            let hasWork = (dailyStats[date] ?? 0) > 0

            if !isWeekend || hasWork {
                workingDaysCount += 1
            }
        }

        // Если нет рабочих дней, возвращаем 0
        guard workingDaysCount > 0 else { return 0 }

        let totalIntervals = dailyStats.values.reduce(0, +)
        let average = Double(totalIntervals) / Double(workingDaysCount)

        print("🔍 Расчет среднего: \(totalIntervals) интервалов / \(workingDaysCount) рабочих дней = \(average)")

        return average
    }
    
    private func calculateWorkingDaysPerWeek(_ dailyStats: [Date: Int]) -> Int {
        // Просто считаем количество дней с интервалами > 0
        let workingDays = dailyStats.values.filter { $0 > 0 }.count
        print("🔍 Рабочих дней в данных: \(workingDays) из \(dailyStats.count) дней")

        // Показываем детали по дням
        let calendar = Calendar.current
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "dd.MM (EEE)"
        dateFormatter.locale = Locale(identifier: "ru_RU")

        let now = Date()
        let today = calendar.startOfDay(for: now)

        for (date, count) in dailyStats.sorted(by: { $0.key < $1.key }) {
            let dayName = dateFormatter.string(from: date)
            let isFuture = date > today
            let status = isFuture ? "(будущий)" : (count > 0 ? "✅" : "❌")
            print("🔍   \(dayName): \(count) интервалов \(status)")
        }

        return workingDays
    }
    
    private func calculateConsistencyScore(_ dailyStats: [Date: Int]) -> Double {
        let values = dailyStats.values.map { Double($0) }
        guard values.count > 1 else { return 1.0 }
        
        let mean = values.reduce(0, +) / Double(values.count)
        let variance = values.map { pow($0 - mean, 2) }.reduce(0, +) / Double(values.count)
        let standardDeviation = sqrt(variance)
        
        // Нормализуем: чем меньше отклонение относительно среднего, тем выше консистентность
        let coefficientOfVariation = mean > 0 ? standardDeviation / mean : 0
        return max(0, 1 - coefficientOfVariation)
    }
    
    private func calculateAverageStartTime(_ intervals: [(Date, TimeInterval)]) -> TimeInterval? {
        guard !intervals.isEmpty else { return nil }

        let calendar = Calendar.current
        var dailyFirstIntervals: [String: Date] = [:]

        // Группируем интервалы по дням и находим первый интервал каждого дня
        for (date, _) in intervals {
            let dayKey = calendar.dateComponents([.year, .month, .day], from: date)
            let dayString = "\(dayKey.year!)-\(dayKey.month!)-\(dayKey.day!)"

            if let existingFirst = dailyFirstIntervals[dayString] {
                // Если уже есть интервал для этого дня, берем более ранний
                if date < existingFirst {
                    dailyFirstIntervals[dayString] = date
                }
            } else {
                // Первый интервал для этого дня
                dailyFirstIntervals[dayString] = date
            }
        }

        // Теперь вычисляем среднее время начала только по первым интервалам дней
        var startTimes: [TimeInterval] = []
        for firstInterval in dailyFirstIntervals.values {
            let components = calendar.dateComponents([.hour, .minute, .second], from: firstInterval)
            let hours = (components.hour ?? 0) * 3600
            let minutes = (components.minute ?? 0) * 60
            let seconds = (components.second ?? 0)
            let secondsFromMidnight = TimeInterval(hours + minutes + seconds)
            startTimes.append(secondsFromMidnight)
        }

        return startTimes.isEmpty ? nil : startTimes.reduce(0, +) / Double(startTimes.count)
    }

    private func calculateAverageIntervalDuration(_ intervals: [(Date, TimeInterval)]) -> TimeInterval {
        guard !intervals.isEmpty else { return 0 }

        let totalDuration = intervals.reduce(0) { $0 + $1.1 }
        return totalDuration / Double(intervals.count)
    }
    
    // MARK: - Анализ рисков
    
    private func identifyRiskFactors(
        intervals: [(Date, TimeInterval)],
        dailyStats: [Date: Int],
        averageIntervalsPerDay: Double,
        averageStartTime: TimeInterval?,
        workingDaysPerWeek: Int,
        consistencyScore: Double,
        averageIntervalDuration: TimeInterval
    ) -> [RiskFactor] {
        var risks: [RiskFactor] = []
        print("🔍 Анализируем риски...")

        // Сначала проверяем прокрастинацию, чтобы учесть её при анализе переработки
        let hasProcrastination = checkForProcrastination(dailyStats: dailyStats, workingDaysPerWeek: workingDaysPerWeek)
        if hasProcrastination.0 {
            risks.append(hasProcrastination.1!)
        }

        // Риск переработки (много интервалов в день ИЛИ интервалы подряд без перерывов ИЛИ слишком длинные интервалы)
        // НО: если есть прокрастинация, то много интервалов в день может быть компенсацией, а не переработкой
        let tooManyIntervals = averageIntervalsPerDay > 7 && !hasProcrastination.0 // Не считаем переработкой, если есть прокрастинация
        let hasConsecutive = hasConsecutiveIntervals(intervals)
        let tooLongIntervals = averageIntervalDuration > 4200 // Больше 70 минут в среднем
        let overtimeCount = intervals.filter { $0.1 > 3600 }.count // Интервалы длиннее 60 минут
        let overtimePercentage = intervals.isEmpty ? 0 : Double(overtimeCount) / Double(intervals.count)

        print("🔍 Проверка переработки:")
        print("🔍   Много интервалов в день (\(averageIntervalsPerDay) > 7): \(averageIntervalsPerDay > 7) (игнорируется из-за прокрастинации: \(hasProcrastination.0))")
        print("🔍   Интервалы подряд без перерывов: \(hasConsecutive)")
        print("🔍   Средняя длительность (\(Int(averageIntervalDuration/60)) мин > 70): \(tooLongIntervals)")
        print("🔍   Переработка в \(Int(overtimePercentage * 100))% интервалов (\(overtimeCount)/\(intervals.count))")

        let hasOverwork = tooManyIntervals || hasConsecutive || tooLongIntervals || overtimePercentage > 0.3
        if hasOverwork {
            print("   ⚠️ Обнаружена переработка!")
            risks.append(.overwork(averagePerDay: averageIntervalsPerDay))
        } else {
            print("   ✅ Переработки не обнаружено")
        }

        // Отдельная проверка для длинных интервалов
        if tooLongIntervals || overtimePercentage > 0.5 {
            print("   ⚠️ Обнаружены слишком длинные интервалы!")
            risks.append(.longIntervals(averageDuration: averageIntervalDuration))
        }

        // Риск непостоянства
        if consistencyScore < 0.6 {
            let variance = 1 - consistencyScore
            risks.append(.inconsistency(variance: variance))
        }
        
        // Риск позднего начала
        if let startTime = averageStartTime {
            let averageHour = Int(startTime / 3600)
            print("🔍 Проверка позднего старта: средний час \(averageHour)")

            // Оптимальные пороги для продуктивности:
            // 7-9 утра - отлично
            // 9-12 утра - нормально
            // 12+ - поздно (влияет на продуктивность)
            if averageHour >= 12 {
                print("   ⚠️ Обнаружен поздний старт! (начало в \(averageHour):xx)")
                risks.append(.lateStart(averageHour: averageHour))
            } else {
                print("   ✅ Время начала работы нормальное (\(averageHour):xx)")
            }
        } else {
            print("🔍 Время начала не определено")
        }
        
        // Риск выгорания (интенсивные дни + дни отдыха)
        let intenseDays = dailyStats.values.filter { $0 > 6 }.count
        let restDays = dailyStats.values.filter { $0 == 0 }.count
        if intenseDays > 2 && restDays > 3 {
            risks.append(.burnoutRisk(intenseDays: intenseDays, restDays: restDays))
        }
        
        // Прокрастинация уже проверена выше, не дублируем
        
        // Приоритизируем риски по важности
        return prioritizeRisks(risks)
    }

    private func prioritizeRisks(_ risks: [RiskFactor]) -> [RiskFactor] {
        // Сортируем риски по приоритету (самые важные первыми)
        return risks.sorted { risk1, risk2 in
            let priority1 = getRiskPriority(risk1)
            let priority2 = getRiskPriority(risk2)
            return priority1 < priority2 // Меньшее число = выше приоритет
        }
    }

    private func getRiskPriority(_ risk: RiskFactor) -> Int {
        switch risk {
        case .procrastination(_):
            return 1 // Самый высокий приоритет - прокрастинация критична
        case .burnoutRisk(_, _):
            return 2 // Риск выгорания - очень важно
        case .overwork(_):
            return 3 // Переработка по количеству
        case .longIntervals(_):
            return 4 // Переработка по времени
        case .lateStart(_):
            return 5 // Поздний старт - менее критично
        case .inconsistency(_):
            return 6 // Нестабильность - наименее критично
        }
    }

    // MARK: - Дополнительные проверки

    private func hasConsecutiveIntervals(_ intervals: [(Date, TimeInterval)]) -> Bool {
        _ = Calendar.current
        let sortedIntervals = intervals.sorted { $0.0 < $1.0 }

        var consecutiveCount = 1
        var maxConsecutive = 1

        for i in 1..<sortedIntervals.count {
            let previousInterval = sortedIntervals[i-1]
            let currentInterval = sortedIntervals[i]

            // Проверяем, идут ли интервалы подряд (разница менее 45 минут)
            let timeDifference = currentInterval.0.timeIntervalSince(previousInterval.0)

            if timeDifference <= 45 * 60 { // 45 минут = 25 мин работы + 20 мин перерыва
                consecutiveCount += 1
                maxConsecutive = max(maxConsecutive, consecutiveCount)
            } else {
                consecutiveCount = 1
            }
        }

        // Считаем переработкой, если более 4 интервалов подряд (2+ часа без длительного перерыва)
        return maxConsecutive > 4
    }

    // MARK: - Генерация рекомендаций
    
    private func generateRecommendations(
        riskFactors: [RiskFactor],
        averageIntervalsPerDay: Double,
        workingDaysPerWeek: Int,
        consistencyScore: Double,
        averageStartTime: TimeInterval?
    ) -> [Recommendation] {
        var recommendations: [Recommendation] = []
        var needsBreaks = false
        var breakReason = ""

        for risk in riskFactors {
            switch risk {
            case .overwork(let average):
                recommendations.append(.reduceWorkload(
                    currentAverage: average,
                    suggested: min(6, average * 0.8)
                ))
                needsBreaks = true
                breakReason = "Обнаружены длительные сессии работы без перерывов"

            case .longIntervals(let averageDuration):
                let currentMinutes = Int(averageDuration / 60)
                needsBreaks = true
                breakReason = "Интервалы слишком длинные (\(currentMinutes) мин). Рекомендуется 25-52 минуты"
                
            case .inconsistency(_):
                recommendations.append(.increaseConsistency(
                    suggestion: "Старайтесь работать примерно одинаковое количество интервалов каждый день"
                ))
                
            case .lateStart(let currentHour):
                // Всегда рекомендуем оптимальное время: 7-8 утра
                let suggestedHour = Int.random(in: 7...8) // Рекомендуем случайно 7 или 8 утра

                recommendations.append(.startEarlier(
                    currentHour: currentHour,
                    suggestedHour: suggestedHour
                ))
                
            case .burnoutRisk(_, _):
                needsBreaks = true
                if breakReason.isEmpty {
                    breakReason = "Обнаружен паттерн интенсивной работы с последующими длительными перерывами"
                }
                
            case .procrastination(_):
                recommendations.append(.establishRoutine(
                    suggestion: "Попробуйте работать хотя бы по 1-2 интервала каждый день"
                ))
            }
        }

        // Добавляем единственную рекомендацию о перерывах, если нужно
        if needsBreaks {
            recommendations.append(.takeBreaks(reason: breakReason))
        }

        // Позитивные рекомендации для хороших показателей
        if riskFactors.isEmpty && consistencyScore > 0.8 && averageIntervalsPerDay >= 3 && averageIntervalsPerDay <= 6 {
            recommendations.append(.maintainBalance(
                praise: "Отличная работа! Вы поддерживаете здоровый и стабильный рабочий ритм"
            ))
        }

        return recommendations
    }

    // MARK: - Проверка прокрастинации

    private func checkForProcrastination(dailyStats: [Date: Int], workingDaysPerWeek: Int) -> (Bool, RiskFactor?) {
        let totalDaysInPeriod = dailyStats.count
        print("🔍 Проверка прокрастинации: \(workingDaysPerWeek) рабочих дней из \(totalDaysInPeriod)")

        // Определяем, сколько дней уже прошло (исключаем будущие дни)
        let calendar = Calendar.current
        let now = Date()
        let today = calendar.startOfDay(for: now)

        let pastDays = dailyStats.keys.filter { $0 <= today }.count
        print("🔍 Прошедших дней в периоде: \(pastDays) из \(totalDaysInPeriod)")

        // Считаем рабочие дни в периоде (пн-пт, исключая выходные)
        let workingDaysInPeriod = dailyStats.keys.filter { date in
            let weekday = calendar.component(.weekday, from: date)
            return weekday >= 2 && weekday <= 6 && date <= today // пн-пт и не в будущем
        }.count

        print("🔍 Рабочих дней в периоде (пн-пт): \(workingDaysInPeriod)")
        print("🔍 Фактически работал: \(workingDaysPerWeek) дней")

        // Простая логика: если работал меньше половины рабочих дней - это прокрастинация
        let expectedWorkingDays = max(1, workingDaysInPeriod / 2)
        print("🔍 Ожидаем минимум: \(expectedWorkingDays) дней из \(workingDaysInPeriod) рабочих")

        if workingDaysPerWeek < expectedWorkingDays {
            let gapDays = workingDaysInPeriod - workingDaysPerWeek // Показываем реальное количество пропущенных рабочих дней
            print("   ⚠️ Обнаружена прокрастинация! Из \(workingDaysInPeriod) рабочих дней работали только \(workingDaysPerWeek)")
            print("   📊 Пропущено рабочих дней: \(gapDays)")
            return (true, .procrastination(gapDays: gapDays))
        } else {
            print("   ✅ Количество рабочих дней нормальное (\(workingDaysPerWeek) из \(workingDaysInPeriod) рабочих дней)")
            return (false, nil)
        }
    }
}

// MARK: - Вспомогательные типы

enum AnalysisPeriod {
    case lastWeek
    case lastMonth
    case lastThreeMonths
    case customWeek(offset: Int) // offset: 0 = текущая неделя, -1 = прошлая, +1 = следующая
    case customRange(start: Date, end: Date) // произвольный диапазон дат
}
