import Cocoa

class ProjectEditDialog: NSWindowController {
    
    // MARK: - Properties
    
    private var projectManager: ProjectManager
    private var project: Project?
    private var completion: ((Project?) -> Void)?
    
    // UI Elements
    private var nameTextField: NSTextField!
    private var typePopUpButton: NSPopUpButton!
    private var colorWell: NSColorWell!
    private var emojiTextField: NSTextField!
    private var emojiButtons: [NSButton] = []
    private var saveButton: NSButton!
    private var cancelButton: NSButton!
    
    // MARK: - Initialization
    
    init(projectManager: ProjectManager, project: Project? = nil, completion: @escaping (Project?) -> Void) {
        print("🔧 ProjectEditDialog: init вызван")
        self.projectManager = projectManager
        self.project = project
        self.completion = completion

        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 480, height: 420),
            styleMask: [.titled, .closable],
            backing: .buffered,
            defer: false
        )

        super.init(window: window)

        print("🔧 ProjectEditDialog: вызываем setupWindow")
        setupWindow()
        print("🔧 ProjectEditDialog: вызываем setupUI")
        setupUI()
        print("🔧 ProjectEditDialog: вызываем setupActions")
        setupActions()
        print("🔧 ProjectEditDialog: вызываем populateFields")
        populateFields()
        print("🔧 ProjectEditDialog: init завершен")
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Window Setup
    
    private func setupWindow() {
        guard let window = window else { return }
        
        window.title = project == nil ? "Создать проект" : "Редактировать проект"
        window.center()
        window.isReleasedWhenClosed = false
        window.delegate = self
    }
    
    private func setupUI() {
        print("🔧 ProjectEditDialog: setupUI начинается")
        NSLog("🔧 ProjectEditDialog: setupUI начинается")

        guard let window = window, let contentView = window.contentView else {
            print("❌ ProjectEditDialog: window или contentView равен nil!")
            NSLog("❌ ProjectEditDialog: window или contentView равен nil!")
            return
        }

        print("🔧 ProjectEditDialog: window и contentView найдены")

        // Создаем основной контейнер
        let containerView = NSView()
        containerView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(containerView)

        print("🔧 ProjectEditDialog: containerView создан")
        
        // Название проекта
        let nameLabel = NSTextField(labelWithString: "Название:")
        nameLabel.translatesAutoresizingMaskIntoConstraints = false
        
        nameTextField = NSTextField()
        nameTextField.translatesAutoresizingMaskIntoConstraints = false
        nameTextField.placeholderString = "Введите название проекта"
        
        // Тип проекта
        let typeLabel = NSTextField(labelWithString: "Тип:")
        typeLabel.translatesAutoresizingMaskIntoConstraints = false
        
        typePopUpButton = NSPopUpButton()
        typePopUpButton.translatesAutoresizingMaskIntoConstraints = false
        
        for projectType in ProjectType.allCases {
            typePopUpButton.addItem(withTitle: "\(projectType.emoji) \(projectType.displayName)")
        }
        
        // Цвет проекта
        let colorLabel = NSTextField(labelWithString: "Цвет:")
        colorLabel.translatesAutoresizingMaskIntoConstraints = false

        colorWell = NSColorWell()
        colorWell.translatesAutoresizingMaskIntoConstraints = false
        colorWell.color = NSColor.systemBlue

        // Кастомный эмодзи
        let emojiLabel = NSTextField(labelWithString: "Кастомная иконка:")
        emojiLabel.translatesAutoresizingMaskIntoConstraints = false

        emojiTextField = NSTextField()
        emojiTextField.translatesAutoresizingMaskIntoConstraints = false
        emojiTextField.placeholderString = "Введите эмодзи или нажмите кнопку ниже"

        // Пояснительный текст
        let helpLabel = NSTextField(labelWithString: "Если не указано, будет использована иконка типа проекта")
        helpLabel.translatesAutoresizingMaskIntoConstraints = false
        helpLabel.font = NSFont.systemFont(ofSize: 11)
        helpLabel.textColor = NSColor.secondaryLabelColor

        // Популярные эмодзи
        let emojiButtonsContainer = NSView()
        emojiButtonsContainer.translatesAutoresizingMaskIntoConstraints = false

        let popularEmojis = ["📝", "💻", "📚", "🎯", "⚡", "🔥", "💡", "🚀"]
        emojiButtons = []

        for emoji in popularEmojis {
            let button = NSButton(title: emoji, target: nil, action: nil)
            button.translatesAutoresizingMaskIntoConstraints = false
            button.isBordered = true
            button.bezelStyle = .rounded
            button.font = NSFont.systemFont(ofSize: 16)
            button.toolTip = "Нажмите чтобы выбрать \(emoji)"
            print("🔧 ProjectEditDialog: Создана кнопка эмодзи '\(emoji)' с target: \(String(describing: button.target)), action: \(String(describing: button.action))")
            emojiButtons.append(button)
            emojiButtonsContainer.addSubview(button)
            print("🔧 ProjectEditDialog: После добавления в контейнер кнопка '\(emoji)' имеет target: \(String(describing: button.target)), action: \(String(describing: button.action))")
        }
        
        // Кнопки
        print("🔧 ProjectEditDialog: Создаем кнопки...")
        NSLog("🔧 ProjectEditDialog: Создаем кнопки...")

        let buttonContainer = NSView()
        buttonContainer.translatesAutoresizingMaskIntoConstraints = false

        print("🔧 ProjectEditDialog: Создаем cancelButton...")
        cancelButton = NSButton(title: "Отмена", target: nil, action: nil)
        cancelButton.translatesAutoresizingMaskIntoConstraints = false
        cancelButton.isEnabled = true
        cancelButton.bezelStyle = .rounded
        print("🔧 ProjectEditDialog: cancelButton создан: \(String(describing: cancelButton))")

        print("🔧 ProjectEditDialog: Создаем saveButton...")
        saveButton = NSButton(title: "Сохранить", target: nil, action: nil)
        saveButton.translatesAutoresizingMaskIntoConstraints = false
        saveButton.keyEquivalent = "\r"
        saveButton.isEnabled = true
        saveButton.bezelStyle = .rounded
        print("🔧 ProjectEditDialog: saveButton создан: \(String(describing: saveButton))")

        buttonContainer.addSubview(cancelButton)
        buttonContainer.addSubview(saveButton)
        
        // Добавляем элементы
        containerView.addSubview(nameLabel)
        containerView.addSubview(nameTextField)
        containerView.addSubview(typeLabel)
        containerView.addSubview(typePopUpButton)
        containerView.addSubview(colorLabel)
        containerView.addSubview(colorWell)
        containerView.addSubview(emojiLabel)
        containerView.addSubview(emojiTextField)
        containerView.addSubview(helpLabel)
        containerView.addSubview(emojiButtonsContainer)
        containerView.addSubview(buttonContainer)

        // Проверяем target/action после добавления в view
        print("🔧 ProjectEditDialog: После добавления в view:")
        print("   saveButton target: \(String(describing: saveButton.target)), action: \(String(describing: saveButton.action))")
        print("   cancelButton target: \(String(describing: cancelButton.target)), action: \(String(describing: cancelButton.action))")
        NSLog("🔧 ProjectEditDialog: После добавления в view - saveButton target: \(String(describing: saveButton.target)), action: \(String(describing: saveButton.action))")
        NSLog("🔧 ProjectEditDialog: После добавления в view - cancelButton target: \(String(describing: cancelButton.target)), action: \(String(describing: cancelButton.action))")
        
        // Constraints
        NSLayoutConstraint.activate([
            // Container
            containerView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 20),
            containerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            containerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            containerView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -20),
            
            // Name
            nameLabel.topAnchor.constraint(equalTo: containerView.topAnchor),
            nameLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            nameLabel.widthAnchor.constraint(equalToConstant: 80),
            
            nameTextField.topAnchor.constraint(equalTo: containerView.topAnchor),
            nameTextField.leadingAnchor.constraint(equalTo: nameLabel.trailingAnchor, constant: 10),
            nameTextField.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            
            // Type
            typeLabel.topAnchor.constraint(equalTo: nameTextField.bottomAnchor, constant: 20),
            typeLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            typeLabel.widthAnchor.constraint(equalToConstant: 80),
            
            typePopUpButton.topAnchor.constraint(equalTo: nameTextField.bottomAnchor, constant: 20),
            typePopUpButton.leadingAnchor.constraint(equalTo: typeLabel.trailingAnchor, constant: 10),
            typePopUpButton.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            
            // Color
            colorLabel.topAnchor.constraint(equalTo: typePopUpButton.bottomAnchor, constant: 20),
            colorLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            colorLabel.widthAnchor.constraint(equalToConstant: 80),
            
            colorWell.topAnchor.constraint(equalTo: typePopUpButton.bottomAnchor, constant: 20),
            colorWell.leadingAnchor.constraint(equalTo: colorLabel.trailingAnchor, constant: 10),
            colorWell.widthAnchor.constraint(equalToConstant: 50),
            colorWell.heightAnchor.constraint(equalToConstant: 30),
            
            // Emoji
            emojiLabel.topAnchor.constraint(equalTo: colorWell.bottomAnchor, constant: 20),
            emojiLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            emojiLabel.widthAnchor.constraint(equalToConstant: 120),

            emojiTextField.topAnchor.constraint(equalTo: colorWell.bottomAnchor, constant: 20),
            emojiTextField.leadingAnchor.constraint(equalTo: emojiLabel.trailingAnchor, constant: 10),
            emojiTextField.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),

            // Help label
            helpLabel.topAnchor.constraint(equalTo: emojiTextField.bottomAnchor, constant: 5),
            helpLabel.leadingAnchor.constraint(equalTo: emojiLabel.trailingAnchor, constant: 10),
            helpLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),

            // Emoji buttons
            emojiButtonsContainer.topAnchor.constraint(equalTo: helpLabel.bottomAnchor, constant: 10),
            emojiButtonsContainer.leadingAnchor.constraint(equalTo: emojiLabel.trailingAnchor, constant: 10),
            emojiButtonsContainer.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            emojiButtonsContainer.heightAnchor.constraint(equalToConstant: 30),

            // Buttons
            buttonContainer.topAnchor.constraint(equalTo: emojiButtonsContainer.bottomAnchor, constant: 30),
            buttonContainer.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            buttonContainer.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            buttonContainer.bottomAnchor.constraint(equalTo: containerView.bottomAnchor),
            buttonContainer.heightAnchor.constraint(equalToConstant: 40),

            cancelButton.leadingAnchor.constraint(equalTo: buttonContainer.leadingAnchor),
            cancelButton.centerYAnchor.constraint(equalTo: buttonContainer.centerYAnchor),

            saveButton.trailingAnchor.constraint(equalTo: buttonContainer.trailingAnchor),
            saveButton.centerYAnchor.constraint(equalTo: buttonContainer.centerYAnchor)
        ])

        // Constraints для кнопок эмодзи
        for (index, button) in emojiButtons.enumerated() {
            NSLayoutConstraint.activate([
                button.topAnchor.constraint(equalTo: emojiButtonsContainer.topAnchor),
                button.bottomAnchor.constraint(equalTo: emojiButtonsContainer.bottomAnchor),
                button.widthAnchor.constraint(equalToConstant: 30)
            ])

            if index == 0 {
                button.leadingAnchor.constraint(equalTo: emojiButtonsContainer.leadingAnchor).isActive = true
            } else {
                button.leadingAnchor.constraint(equalTo: emojiButtons[index - 1].trailingAnchor, constant: 5).isActive = true
            }
        }

        print("🔧 ProjectEditDialog: setupUI завершен успешно")
        print("🔧 ProjectEditDialog: После setupUI - cancelButton: \(String(describing: cancelButton))")
        print("🔧 ProjectEditDialog: После setupUI - saveButton: \(String(describing: saveButton))")
        NSLog("🔧 ProjectEditDialog: setupUI завершен успешно")
        NSLog("🔧 ProjectEditDialog: После setupUI - cancelButton: \(String(describing: cancelButton))")
        NSLog("🔧 ProjectEditDialog: После setupUI - saveButton: \(String(describing: saveButton))")
    }

    // MARK: - Actions Setup

    private func setupActions() {
        print("🔧 ProjectEditDialog: setupActions вызван")
        NSLog("🔧 ProjectEditDialog: setupActions вызван")

        print("🔧 ProjectEditDialog: self = \(self)")
        NSLog("🔧 ProjectEditDialog: self = \(self)")

        // Устанавливаем target и action ПОСЛЕ добавления в view hierarchy
        print("🔧 ProjectEditDialog: Проверяем кнопки...")
        print("🔧 ProjectEditDialog: cancelButton = \(String(describing: cancelButton))")
        print("🔧 ProjectEditDialog: saveButton = \(String(describing: saveButton))")

        guard let cancelBtn = cancelButton else {
            print("❌ ProjectEditDialog: cancelButton равен nil!")
            NSLog("❌ ProjectEditDialog: cancelButton равен nil!")
            return
        }

        guard let saveBtn = saveButton else {
            print("❌ ProjectEditDialog: saveButton равен nil!")
            NSLog("❌ ProjectEditDialog: saveButton равен nil!")
            return
        }

        print("🔧 ProjectEditDialog: Устанавливаем target для cancelButton...")
        cancelBtn.target = self
        cancelBtn.action = #selector(cancelAction)
        print("🔧 ProjectEditDialog: cancelButton.target установлен: \(String(describing: cancelBtn.target))")
        print("🔧 ProjectEditDialog: cancelButton.action установлен: \(String(describing: cancelBtn.action))")

        print("🔧 ProjectEditDialog: Устанавливаем target для saveButton...")
        saveBtn.target = self
        saveBtn.action = #selector(saveAction)
        print("🔧 ProjectEditDialog: saveButton.target установлен: \(String(describing: saveBtn.target))")
        print("🔧 ProjectEditDialog: saveButton.action установлен: \(String(describing: saveBtn.action))")

        // Устанавливаем target и action для кнопок эмодзи
        print("🔧 ProjectEditDialog: Устанавливаем target для \(emojiButtons.count) кнопок эмодзи...")
        for (index, button) in emojiButtons.enumerated() {
            button.target = self
            button.action = #selector(emojiButtonClicked(_:))
            print("🔧 ProjectEditDialog: Кнопка эмодзи \(index): target=\(String(describing: button.target)), action=\(String(describing: button.action))")
        }

        print("🔧 ProjectEditDialog: setupActions завершен")
        NSLog("🔧 ProjectEditDialog: setupActions завершен")
    }

    // MARK: - Data Population

    private func populateFields() {
        guard let project = project else { return }

        nameTextField.stringValue = project.name

        // Устанавливаем тип проекта
        if let typeIndex = ProjectType.allCases.firstIndex(of: project.type) {
            typePopUpButton.selectItem(at: typeIndex)
        }

        // Устанавливаем цвет
        if let colorString = project.color {
            colorWell.color = NSColor(hex: colorString) ?? NSColor.systemBlue
        }

        // Устанавливаем кастомный эмодзи
        emojiTextField.stringValue = project.customEmoji ?? ""
    }
    
    // MARK: - Actions
    
    @objc private func saveAction() {
        print("🔧 ProjectEditDialog: saveAction вызван")
        NSLog("🔧 ProjectEditDialog: saveAction вызван")
        print("🔧 ProjectEditDialog: saveAction - начинаем проверки")
        NSLog("🔧 ProjectEditDialog: saveAction - начинаем проверки")

        let nameValue = nameTextField.stringValue.trimmingCharacters(in: .whitespacesAndNewlines)
        print("🔧 ProjectEditDialog: saveAction - имя проекта: '\(nameValue)'")
        NSLog("🔧 ProjectEditDialog: saveAction - имя проекта: '\(nameValue)'")

        guard !nameValue.isEmpty else {
            print("🔧 ProjectEditDialog: saveAction - имя пустое, показываем alert")
            NSLog("🔧 ProjectEditDialog: saveAction - имя пустое, показываем alert")
            showAlert(title: "Ошибка", message: "Введите название проекта")
            return
        }

        print("🔧 ProjectEditDialog: saveAction - собираем данные")
        NSLog("🔧 ProjectEditDialog: saveAction - собираем данные")

        let name = nameTextField.stringValue.trimmingCharacters(in: .whitespacesAndNewlines)
        let selectedTypeIndex = typePopUpButton.indexOfSelectedItem
        let projectType = ProjectType.allCases[selectedTypeIndex]
        let color = colorWell.color.hexString
        let customEmoji = emojiTextField.stringValue.trimmingCharacters(in: .whitespacesAndNewlines)
        let finalEmoji = customEmoji.isEmpty ? nil : customEmoji

        print("🔧 ProjectEditDialog: saveAction - данные: name='\(name)', type=\(projectType), color=\(color), emoji='\(finalEmoji ?? "nil")'")
        NSLog("🔧 ProjectEditDialog: saveAction - данные: name='\(name)', type=\(projectType), color=\(color), emoji='\(finalEmoji ?? "nil")'")

        if let existingProject = project {
            print("🔧 ProjectEditDialog: saveAction - редактируем существующий проект")
            NSLog("🔧 ProjectEditDialog: saveAction - редактируем существующий проект")
            // Редактируем существующий проект
            var updatedProject = existingProject
            updatedProject.name = name
            updatedProject.type = projectType
            updatedProject.color = color
            updatedProject.customEmoji = finalEmoji

            projectManager.updateProject(updatedProject)
            print("🔧 ProjectEditDialog: saveAction - вызываем completion с обновленным проектом")
            NSLog("🔧 ProjectEditDialog: saveAction - вызываем completion с обновленным проектом")
            completion?(updatedProject)
        } else {
            print("🔧 ProjectEditDialog: saveAction - создаем новый проект")
            NSLog("🔧 ProjectEditDialog: saveAction - создаем новый проект")
            // Создаем новый проект
            let newProject = projectManager.createProject(
                name: name,
                type: projectType,
                color: color,
                customEmoji: finalEmoji
            )
            print("🔧 ProjectEditDialog: saveAction - вызываем completion с новым проектом")
            NSLog("🔧 ProjectEditDialog: saveAction - вызываем completion с новым проектом")
            completion?(newProject)
        }

        print("🔧 ProjectEditDialog: saveAction - закрываем диалог")
        NSLog("🔧 ProjectEditDialog: saveAction - закрываем диалог")
        close()
    }
    
    @objc private func cancelAction() {
        print("🔧 ProjectEditDialog: cancelAction вызван")
        NSLog("🔧 ProjectEditDialog: cancelAction вызван")
        completion?(nil)
        close()
    }

    @objc private func emojiButtonClicked(_ sender: NSButton) {
        print("🔧 ProjectEditDialog: emojiButtonClicked вызван с \(sender.title)")
        NSLog("🔧 ProjectEditDialog: emojiButtonClicked вызван с \(sender.title)")
        emojiTextField.stringValue = sender.title
    }
    
    private func showAlert(title: String, message: String) {
        let alert = NSAlert()
        alert.messageText = title
        alert.informativeText = message
        alert.addButton(withTitle: "OK")
        alert.runModal()
    }
    
    // MARK: - Public Methods
    
    func showDialog() {
        window?.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)

        // Устанавливаем фокус на поле имени
        window?.makeFirstResponder(nameTextField)

        print("🔧 ProjectEditDialog: showDialog вызван, окно показано")
    }
}

// MARK: - NSWindowDelegate

extension ProjectEditDialog: NSWindowDelegate {
    func windowWillClose(_ notification: Notification) {
        completion?(nil)
    }
}

// MARK: - NSColor Extension

extension NSColor {
    convenience init?(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            return nil
        }
        
        self.init(
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            alpha: Double(a) / 255
        )
    }
    
    var hexString: String {
        guard let rgbColor = usingColorSpace(.deviceRGB) else { return "#000000" }
        let red = Int(round(rgbColor.redComponent * 255))
        let green = Int(round(rgbColor.greenComponent * 255))
        let blue = Int(round(rgbColor.blueComponent * 255))
        return String(format: "#%02X%02X%02X", red, green, blue)
    }
}
