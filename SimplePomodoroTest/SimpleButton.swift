import Cocoa

class SimpleButton: NSButton {
    
    private let isPrimary: Bool
    private var originalBackgroundColor: NSColor
    
    init(title: String, isPrimary: Bool = false) {
        self.isPrimary = isPrimary
        
        // Устанавливаем цвета
        if isPrimary {
            self.originalBackgroundColor = NSColor(red: 0.0, green: 0.48, blue: 1.0, alpha: 1.0)
        } else {
            self.originalBackgroundColor = NSColor(red: 0.45, green: 0.45, blue: 0.5, alpha: 1.0)
        }
        
        super.init(frame: .zero)
        
        self.title = title
        setupButton()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupButton() {
        // Основные настройки
        self.isBordered = false
        self.wantsLayer = true
        
        // Стиль текста
        self.font = NSFont.systemFont(ofSize: 14, weight: .medium)
        
        // Настройка слоя
        setupLayer()
        
        // Цвет текста
        if isPrimary {
            self.contentTintColor = NSColor.white
        } else {
            self.contentTintColor = NSColor.white
        }
    }
    
    private func setupLayer() {
        guard let layer = self.layer else { return }
        
        // Фон
        layer.backgroundColor = originalBackgroundColor.cgColor
        
        // Скругление
        layer.cornerRadius = 8
        
        // Тень
        layer.shadowColor = NSColor.black.cgColor
        layer.shadowOpacity = 0.2
        layer.shadowOffset = CGSize(width: 0, height: 2)
        layer.shadowRadius = 4
        
        // Граница
        layer.borderColor = NSColor.white.withAlphaComponent(0.1).cgColor
        layer.borderWidth = 1
    }
    
    // MARK: - Mouse Events (только изменение цвета, никаких трансформаций)
    
    override func mouseEntered(with event: NSEvent) {
        super.mouseEntered(with: event)
        
        // Только изменение цвета фона
        if isPrimary {
            layer?.backgroundColor = NSColor(red: 0.1, green: 0.58, blue: 1.0, alpha: 1.0).cgColor
        } else {
            layer?.backgroundColor = NSColor(red: 0.55, green: 0.55, blue: 0.6, alpha: 1.0).cgColor
        }
    }
    
    override func mouseExited(with event: NSEvent) {
        super.mouseExited(with: event)
        
        // Возвращаем исходный цвет
        layer?.backgroundColor = originalBackgroundColor.cgColor
    }
    
    override func mouseDown(with event: NSEvent) {
        super.mouseDown(with: event)
        
        // Только затемнение
        if isPrimary {
            layer?.backgroundColor = NSColor(red: 0.0, green: 0.38, blue: 0.85, alpha: 1.0).cgColor
        } else {
            layer?.backgroundColor = NSColor(red: 0.35, green: 0.35, blue: 0.4, alpha: 1.0).cgColor
        }
    }
    
    override func mouseUp(with event: NSEvent) {
        super.mouseUp(with: event)
        
        // Возвращаем hover цвет
        if isPrimary {
            layer?.backgroundColor = NSColor(red: 0.1, green: 0.58, blue: 1.0, alpha: 1.0).cgColor
        } else {
            layer?.backgroundColor = NSColor(red: 0.55, green: 0.55, blue: 0.6, alpha: 1.0).cgColor
        }
    }
    
    override func updateTrackingAreas() {
        super.updateTrackingAreas()
        
        // Удаляем старые tracking areas
        for trackingArea in trackingAreas {
            removeTrackingArea(trackingArea)
        }
        
        // Добавляем новую tracking area
        let trackingArea = NSTrackingArea(
            rect: bounds,
            options: [.mouseEnteredAndExited, .activeInKeyWindow],
            owner: self,
            userInfo: nil
        )
        addTrackingArea(trackingArea)
    }
    
    override func layout() {
        super.layout()
        // Обновляем размер слоя
        layer?.frame = bounds
    }
}
