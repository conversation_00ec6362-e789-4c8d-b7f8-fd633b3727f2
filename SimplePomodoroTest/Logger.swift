import Foundation
import Cocoa

/// Уровни логирования
enum LogLevel: String, CaseIterable {
    case debug = "DEBUG"
    case info = "INFO"
    case warning = "WARNING"
    case error = "ERROR"
    case critical = "CRITICAL"

    var emoji: String {
        switch self {
        case .debug: return "🔍"
        case .info: return "💡"
        case .warning: return "⚠️"
        case .error: return "❌"
        case .critical: return "🚨"
        }
    }

    var shortName: String {
        switch self {
        case .debug: return "DBG"
        case .info: return "INF"
        case .warning: return "WRN"
        case .error: return "ERR"
        case .critical: return "CRT"
        }
    }
}

/// Централизованная система логирования для диагностики крашей
class Logger {
    static let shared = Logger()
    
    private var logDirectory: URL
    private let currentLogFile: URL
    private let maxLogFileSize: Int = 2 * 1024 * 1024 // 2MB для компактности
    private let maxLogFiles: Int = 3 // Храним только 3 последних файла
    private let dateFormatter: DateFormatter
    private let timeFormatter: DateFormatter
    private let fileManager = FileManager.default
    private var logQueue = DispatchQueue(label: "com.uprod.logger", qos: .utility)
    
    // Мониторинг памяти
    private var memoryTimer: Timer?
    private var lastMemoryCheck: Date = Date()
    
    private init() {
        // Создаем директорию для логов в Application Support (sandbox совместимо)
        let appSupport = fileManager.urls(for: .applicationSupportDirectory, in: .userDomainMask).first!
        logDirectory = appSupport.appendingPathComponent("uProd/Logs")

        // Создаем директорию если её нет
        do {
            try fileManager.createDirectory(at: logDirectory, withIntermediateDirectories: true)
            print("📁 Логи будут сохраняться в: \(logDirectory.path)")
        } catch {
            print("❌ Ошибка создания директории логов: \(error)")
            // Fallback к временной директории
            logDirectory = URL(fileURLWithPath: NSTemporaryDirectory()).appendingPathComponent("uProd_Logs")
            try? fileManager.createDirectory(at: logDirectory, withIntermediateDirectories: true)
            print("📁 Используем временную директорию: \(logDirectory.path)")
        }
        
        // Текущий лог файл
        let timestamp = DateFormatter.logFileFormatter.string(from: Date())
        currentLogFile = logDirectory.appendingPathComponent("uProd_\(timestamp).log")
        
        // Настраиваем форматтеры
        dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        dateFormatter.locale = Locale(identifier: "en_US_POSIX")

        timeFormatter = DateFormatter()
        timeFormatter.dateFormat = "HH:mm:ss"
        timeFormatter.locale = Locale(identifier: "en_US_POSIX")
        
        // Инициализируем лог файл
        initializeLogFile()
        
        // Очищаем старые логи
        cleanupOldLogs()
        
        // Запускаем мониторинг памяти (реже для экономии места)
        startMemoryMonitoring()

        // Логируем запуск системы логирования
        log(.info, "System", "🚀 Система логирования запущена")
        log(.debug, "System", "📁 Лог файл: \(currentLogFile.lastPathComponent)")
    }
    
    deinit {
        memoryTimer?.invalidate()
    }
    
    /// Основной метод логирования
    func log(_ level: LogLevel, _ category: String, _ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        let time = timeFormatter.string(from: Date())
        let categoryShort = String(category.prefix(8)).padding(toLength: 8, withPad: " ", startingAt: 0)

        // Компактный и красивый формат: время | уровень | категория | эмодзи сообщение
        let logMessage = "\(time) [\(level.shortName)] \(categoryShort) \(level.emoji) \(message)\n"

        // Асинхронно записываем в файл
        logQueue.async { [weak self] in
            self?.writeToFile(logMessage)
        }

        // Для критических ошибок также выводим в консоль
        if level == .critical || level == .error {
            NSLog("🚨 uProd: \(message)")
        }
    }
    
    /// Логирование с дополнительными данными
    func logWithData(_ level: LogLevel, _ category: String, _ message: String, data: [String: Any] = [:], file: String = #file, function: String = #function, line: Int = #line) {
        var fullMessage = message
        if !data.isEmpty {
            // Компактное отображение данных
            let dataString = data.compactMap { key, value in
                "\(key)=\(value)"
            }.joined(separator: " ")
            fullMessage += " [\(dataString)]"
        }
        log(level, category, fullMessage, file: file, function: function, line: line)
    }
    
    /// Логирование состояния памяти
    func logMemoryUsage() {
        let memoryInfo = getMemoryUsage()
        if let memoryMB = memoryInfo["memory_used_mb"] as? String {
            log(.debug, "Memory", "💾 \(memoryMB)MB")
        }
    }
    
    /// Логирование краша
    func logCrash(_ message: String, exception: NSException? = nil, signal: Int32? = nil) {
        var crashData: [String: Any] = [:]
        
        if let exception = exception {
            crashData["exception_name"] = exception.name.rawValue
            crashData["exception_reason"] = exception.reason ?? "Unknown"
            crashData["exception_callstack"] = exception.callStackSymbols.joined(separator: "\n")
        }
        
        if let signal = signal {
            crashData["signal"] = signal
            crashData["signal_name"] = signalName(signal)
        }
        
        crashData.merge(getMemoryUsage()) { _, new in new }
        crashData.merge(getSystemInfo()) { _, new in new }
        
        logWithData(.critical, "CRASH", message, data: crashData)
        
        // Принудительно сбрасываем буфер
        logQueue.sync {
            // Синхронная запись для краша
        }
    }
    
    // MARK: - Private Methods
    
    private func initializeLogFile() {
        let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown"
        let header = """
        🚀 uProd v\(version) - \(dateFormatter.string(from: Date()))
        📱 \(ProcessInfo.processInfo.operatingSystemVersionString)
        ═══════════════════════════════════════════════════════════

        """

        try? header.write(to: currentLogFile, atomically: true, encoding: .utf8)
    }
    
    private func writeToFile(_ message: String) {
        guard let data = message.data(using: .utf8) else { return }
        
        if fileManager.fileExists(atPath: currentLogFile.path) {
            if let fileHandle = try? FileHandle(forWritingTo: currentLogFile) {
                fileHandle.seekToEndOfFile()
                fileHandle.write(data)
                fileHandle.closeFile()
            }
        } else {
            try? data.write(to: currentLogFile)
        }
        
        // Проверяем размер файла и ротируем если нужно
        checkLogRotation()
    }
    
    private func checkLogRotation() {
        guard let attributes = try? fileManager.attributesOfItem(atPath: currentLogFile.path),
              let fileSize = attributes[.size] as? Int,
              fileSize > maxLogFileSize else { return }
        
        // Ротируем лог
        rotateLog()
    }
    
    private func rotateLog() {
        // Создаем новый лог файл
        let timestamp = DateFormatter.logFileFormatter.string(from: Date())
        let newLogFile = logDirectory.appendingPathComponent("uProd_\(timestamp).log")
        
        // Обновляем текущий файл (это делается в основном потоке)
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            // Здесь можно обновить currentLogFile, но для простоты оставляем как есть
        }
        
        cleanupOldLogs()
    }
    
    private func cleanupOldLogs() {
        do {
            let logFiles = try fileManager.contentsOfDirectory(at: logDirectory, includingPropertiesForKeys: [.creationDateKey])
                .filter { $0.pathExtension == "log" }
                .sorted { file1, file2 in
                    let date1 = (try? file1.resourceValues(forKeys: [.creationDateKey]))?.creationDate ?? Date.distantPast
                    let date2 = (try? file2.resourceValues(forKeys: [.creationDateKey]))?.creationDate ?? Date.distantPast
                    return date1 > date2
                }
            
            // Удаляем старые файлы, оставляя только maxLogFiles
            if logFiles.count > maxLogFiles {
                for i in maxLogFiles..<logFiles.count {
                    try? fileManager.removeItem(at: logFiles[i])
                }
            }
        } catch {
            NSLog("Ошибка очистки старых логов: \(error)")
        }
    }
    
    private func startMemoryMonitoring() {
        // Логируем память каждые 5 минут для экономии места
        memoryTimer = Timer.scheduledTimer(withTimeInterval: 300.0, repeats: true) { [weak self] _ in
            self?.logMemoryUsage()
        }
    }
    
    private func getMemoryUsage() -> [String: Any] {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            let usedMB = Double(info.resident_size) / 1024.0 / 1024.0
            return [
                "memory_used_mb": String(format: "%.2f", usedMB),
                "memory_used_bytes": info.resident_size
            ]
        }
        
        return ["memory_error": "Failed to get memory info"]
    }
    
    private func getSystemInfo() -> [String: Any] {
        return [
            "uptime": ProcessInfo.processInfo.systemUptime,
            "active_processor_count": ProcessInfo.processInfo.activeProcessorCount,
            "physical_memory": ProcessInfo.processInfo.physicalMemory / 1024 / 1024, // MB
            "thermal_state": ProcessInfo.processInfo.thermalState.rawValue
        ]
    }
    
    private func signalName(_ signal: Int32) -> String {
        switch signal {
        case SIGABRT: return "SIGABRT"
        case SIGSEGV: return "SIGSEGV"
        case SIGBUS: return "SIGBUS"
        case SIGILL: return "SIGILL"
        case SIGFPE: return "SIGFPE"
        case SIGPIPE: return "SIGPIPE"
        default: return "SIGNAL_\(signal)"
        }
    }
}

// MARK: - Extensions

extension DateFormatter {
    static let logFileFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"
        formatter.locale = Locale(identifier: "en_US_POSIX")
        return formatter
    }()
}

// MARK: - Convenience Methods

/// Глобальные функции для удобного логирования
func logDebug(_ category: String, _ message: String, file: String = #file, function: String = #function, line: Int = #line) {
    Logger.shared.log(.debug, category, message, file: file, function: function, line: line)
}

func logInfo(_ category: String, _ message: String, file: String = #file, function: String = #function, line: Int = #line) {
    Logger.shared.log(.info, category, message, file: file, function: function, line: line)
}

func logWarning(_ category: String, _ message: String, file: String = #file, function: String = #function, line: Int = #line) {
    Logger.shared.log(.warning, category, message, file: file, function: function, line: line)
}

func logError(_ category: String, _ message: String, file: String = #file, function: String = #function, line: Int = #line) {
    Logger.shared.log(.error, category, message, file: file, function: function, line: line)
}

func logCritical(_ category: String, _ message: String, file: String = #file, function: String = #function, line: Int = #line) {
    Logger.shared.log(.critical, category, message, file: file, function: function, line: line)
}
