import Cocoa

protocol ProjectFormDelegate: AnyObject {
    func projectFormDidSave(_ form: ProjectForm, project: Project)
    func projectFormDidCancel(_ form: ProjectForm)
}

class ProjectForm: NSView {
    
    // MARK: - Properties
    
    weak var delegate: ProjectFormDelegate?
    private var editingProject: Project? // nil для создания, проект для редактирования
    private var currentPopover: NSPopover? // Для хранения текущего popover
    
    // UI Elements
    private var overlay: NSView!
    private var formView: NSView!
    private var titleLabel: NSTextField!
    private var nameLabel: NSTextField!
    private var nameField: NSTextField!
    private var emojiLabel: NSTextField!
    private var emojiButton: NSButton!
    private var typeLabel: NSTextField!
    private var typePopup: NSPopUpButton!
    private var cancelButton: NSButton!
    private var saveButton: NSButton!
    
    // MARK: - Initialization
    
    init(project: Project? = nil) {
        self.editingProject = project
        super.init(frame: .zero)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        translatesAutoresizingMaskIntoConstraints = false
        
        // Создаем overlay
        overlay = NSView()
        overlay.wantsLayer = true
        overlay.layer?.backgroundColor = NSColor.black.withAlphaComponent(0.5).cgColor
        overlay.translatesAutoresizingMaskIntoConstraints = false
        addSubview(overlay)
        
        // Создаем форму
        formView = NSView()
        formView.wantsLayer = true
        formView.layer?.backgroundColor = NSColor.controlBackgroundColor.cgColor
        formView.layer?.cornerRadius = 12
        formView.translatesAutoresizingMaskIntoConstraints = false
        overlay.addSubview(formView)
        
        setupFormElements()
        setupConstraints()
        populateFields()
        
        // Анимация появления
        overlay.layer?.opacity = 0
        NSAnimationContext.runAnimationGroup { context in
            context.duration = 0.2
            overlay.animator().layer?.opacity = 1
        }
    }
    
    private func setupFormElements() {
        // Заголовок
        let title = editingProject != nil ? "Редактировать проект" : "Создать проект"
        titleLabel = NSTextField(labelWithString: title)
        titleLabel.font = NSFont.systemFont(ofSize: 18, weight: .bold)
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        formView.addSubview(titleLabel)
        
        // Поле названия
        nameLabel = NSTextField(labelWithString: "Название:")
        nameLabel.translatesAutoresizingMaskIntoConstraints = false
        formView.addSubview(nameLabel)
        
        nameField = NSTextField()
        nameField.translatesAutoresizingMaskIntoConstraints = false
        formView.addSubview(nameField)
        
        // Поле эмодзи
        emojiLabel = NSTextField(labelWithString: "Эмодзи:")
        emojiLabel.translatesAutoresizingMaskIntoConstraints = false
        formView.addSubview(emojiLabel)
        
        // Простая кнопка с эмодзи
        emojiButton = NSButton()
        emojiButton.translatesAutoresizingMaskIntoConstraints = false
        emojiButton.title = "📁" // Иконка по умолчанию
        emojiButton.font = NSFont.systemFont(ofSize: 32)
        emojiButton.bezelStyle = .rounded
        emojiButton.target = self
        emojiButton.action = #selector(showEmojiMenu)
        emojiButton.toolTip = "Нажмите для выбора иконки"

        formView.addSubview(emojiButton)
        
        // Поле типа
        typeLabel = NSTextField(labelWithString: "Тип:")
        typeLabel.translatesAutoresizingMaskIntoConstraints = false
        formView.addSubview(typeLabel)
        
        typePopup = NSPopUpButton()
        typePopup.translatesAutoresizingMaskIntoConstraints = false
        
        // Добавляем типы проектов
        for projectType in ProjectType.allCases {
            typePopup.addItem(withTitle: "\(projectType.emoji) \(projectType.displayName)")
        }
        
        formView.addSubview(typePopup)
        
        // Кнопки
        cancelButton = createStableButton(title: "Отмена", isPrimary: false)
        cancelButton.target = self
        cancelButton.action = #selector(cancelClicked)
        formView.addSubview(cancelButton)
        
        let saveTitle = editingProject != nil ? "Сохранить" : "Создать проект"
        saveButton = createStableButton(title: saveTitle, isPrimary: true)
        saveButton.target = self
        saveButton.action = #selector(saveClicked)
        formView.addSubview(saveButton)
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // Overlay
            overlay.topAnchor.constraint(equalTo: topAnchor),
            overlay.leadingAnchor.constraint(equalTo: leadingAnchor),
            overlay.trailingAnchor.constraint(equalTo: trailingAnchor),
            overlay.bottomAnchor.constraint(equalTo: bottomAnchor),
            
            // Form
            formView.centerXAnchor.constraint(equalTo: overlay.centerXAnchor),
            formView.centerYAnchor.constraint(equalTo: overlay.centerYAnchor),
            formView.widthAnchor.constraint(equalToConstant: 400),
            formView.heightAnchor.constraint(equalToConstant: 380),
            
            // Title
            titleLabel.topAnchor.constraint(equalTo: formView.topAnchor, constant: 20),
            titleLabel.centerXAnchor.constraint(equalTo: formView.centerXAnchor),
            
            // Name
            nameLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 30),
            nameLabel.leadingAnchor.constraint(equalTo: formView.leadingAnchor, constant: 20),
            
            nameField.topAnchor.constraint(equalTo: nameLabel.bottomAnchor, constant: 8),
            nameField.leadingAnchor.constraint(equalTo: formView.leadingAnchor, constant: 20),
            nameField.trailingAnchor.constraint(equalTo: formView.trailingAnchor, constant: -20),
            
            // Emoji
            emojiLabel.topAnchor.constraint(equalTo: nameField.bottomAnchor, constant: 20),
            emojiLabel.leadingAnchor.constraint(equalTo: formView.leadingAnchor, constant: 20),
            
            // Emoji Button
            emojiButton.topAnchor.constraint(equalTo: emojiLabel.bottomAnchor, constant: 8),
            emojiButton.leadingAnchor.constraint(equalTo: formView.leadingAnchor, constant: 20),
            emojiButton.widthAnchor.constraint(equalToConstant: 60),
            emojiButton.heightAnchor.constraint(equalToConstant: 50),
            
            // Type
            typeLabel.topAnchor.constraint(equalTo: emojiButton.bottomAnchor, constant: 20),
            typeLabel.leadingAnchor.constraint(equalTo: formView.leadingAnchor, constant: 20),
            
            typePopup.topAnchor.constraint(equalTo: typeLabel.bottomAnchor, constant: 8),
            typePopup.leadingAnchor.constraint(equalTo: formView.leadingAnchor, constant: 20),
            typePopup.trailingAnchor.constraint(equalTo: formView.trailingAnchor, constant: -20),
            
            // Buttons
            cancelButton.bottomAnchor.constraint(equalTo: formView.bottomAnchor, constant: -20),
            cancelButton.leadingAnchor.constraint(equalTo: formView.leadingAnchor, constant: 20),
            cancelButton.widthAnchor.constraint(equalToConstant: 100),
            cancelButton.heightAnchor.constraint(equalToConstant: 32),
            
            saveButton.bottomAnchor.constraint(equalTo: formView.bottomAnchor, constant: -20),
            saveButton.trailingAnchor.constraint(equalTo: formView.trailingAnchor, constant: -20),
            saveButton.widthAnchor.constraint(equalToConstant: 120),
            saveButton.heightAnchor.constraint(equalToConstant: 32)
        ])
    }
    
    private func populateFields() {
        if let project = editingProject {
            // Редактирование - заполняем поля
            nameField.stringValue = project.name

            // Показываем текущую иконку
            if let customEmoji = project.customEmoji, !customEmoji.isEmpty {
                emojiButton.title = customEmoji
            } else {
                emojiButton.title = project.type.emoji
            }

            // Выбираем правильный тип в popup
            if let index = ProjectType.allCases.firstIndex(of: project.type) {
                typePopup.selectItem(at: index)
            }
        } else {
            // Создание - устанавливаем значения по умолчанию
            nameField.stringValue = ""
            emojiButton.title = "📁" // Иконка по умолчанию
            typePopup.selectItem(at: 0) // Первый тип по умолчанию
        }
    }
    
    // MARK: - Actions
    
    @objc private func cancelClicked() {
        print("🚫 Отмена формы проекта")
        NSLog("🚫 Отмена формы проекта")
        closeWithAnimation {
            self.delegate?.projectFormDidCancel(self)
        }
    }
    
    @objc private func showEmojiMenu() {
        // Популярные эмодзи для проектов (5 рядов по 6)
        let emojis = [
            "📁", "💼", "🎯", "⚡", "🚀", "💡",
            "🔥", "⭐", "🎨", "🔧", "📊", "💻",
            "📝", "🏠", "🎵", "📱", "🌟", "💎",
            "🎮", "📚", "🔔", "⚙️", "🎪", "🎭",
            "🎬", "📷", "🎸", "🏆", "🎲", "🎳"
        ]

        // Создаем всплывающее окно
        let popover = NSPopover()

        // Параметры сетки
        let buttonSize: CGFloat = 30
        let spacing: CGFloat = 6
        let padding: CGFloat = 16  // Одинаковые отступы со всех сторон
        let cols = 6
        let rows = 5

        // Вычисляем размер popover с учетом отступов
        let contentWidth = CGFloat(cols) * buttonSize + CGFloat(cols - 1) * spacing + padding * 2
        let contentHeight = CGFloat(rows) * buttonSize + CGFloat(rows - 1) * spacing + padding * 2

        popover.contentSize = NSSize(width: contentWidth, height: contentHeight)
        popover.behavior = .transient
        popover.animates = true

        // Создаем контроллер для содержимого
        let viewController = NSViewController()
        let containerView = NSView()
        containerView.frame = NSRect(x: 0, y: 0, width: contentWidth, height: contentHeight)
        viewController.view = containerView

        // Создаем сетку кнопок с одинаковыми отступами
        for (index, emoji) in emojis.enumerated() {
            let row = index / cols
            let col = index % cols

            let x = padding + CGFloat(col) * (buttonSize + spacing)
            let y = padding + CGFloat(rows - 1 - row) * (buttonSize + spacing) // Инвертируем Y

            let button = NSButton(frame: NSRect(x: x, y: y, width: buttonSize, height: buttonSize))
            button.title = emoji
            button.font = NSFont.systemFont(ofSize: 18)
            button.isBordered = false
            button.bezelStyle = .rounded
            button.target = self
            button.action = #selector(selectEmojiFromPopover(_:))

            // Сохраняем эмодзи в кнопке
            button.identifier = NSUserInterfaceItemIdentifier(emoji)

            containerView.addSubview(button)
        }

        // Сохраняем popover как свойство класса для закрытия
        self.currentPopover = popover

        popover.contentViewController = viewController
        popover.show(relativeTo: emojiButton.bounds, of: emojiButton, preferredEdge: .minY)
    }

    @objc private func selectEmojiFromPopover(_ sender: NSButton) {
        if let emoji = sender.identifier?.rawValue {
            emojiButton.title = emoji

            // Закрываем popover
            currentPopover?.close()
            currentPopover = nil
        }
    }

    @objc private func selectEmoji(_ sender: NSMenuItem) {
        if let emoji = sender.representedObject as? String {
            emojiButton.title = emoji
            print("✅ Выбран эмодзи: \(emoji)")
        }
    }


    @objc private func saveClicked() {
        let name = nameField.stringValue.trimmingCharacters(in: .whitespacesAndNewlines)
        let emoji = emojiButton.title.trimmingCharacters(in: .whitespacesAndNewlines)
        
        guard !name.isEmpty else {
            showError("Введите название проекта")
            return
        }
        
        let selectedType = ProjectType.allCases[typePopup.indexOfSelectedItem]
        
        let project: Project
        if let existingProject = editingProject {
            // Редактирование
            var updatedProject = existingProject
            updatedProject.name = name
            updatedProject.customEmoji = emoji.isEmpty ? nil : emoji
            updatedProject.type = selectedType
            project = updatedProject
            
            print("📝 Проект обновлен: \(project.name)")
            NSLog("📝 Проект обновлен: \(project.name)")
        } else {
            // Создание
            project = Project(
                name: name,
                type: selectedType,
                customEmoji: emoji.isEmpty ? nil : emoji
            )
            
            print("🎯 Проект создан: \(project.name)")
            NSLog("🎯 Проект создан: \(project.name)")
        }
        
        closeWithAnimation {
            self.delegate?.projectFormDidSave(self, project: project)
        }
    }
    
    private func closeWithAnimation(completion: @escaping () -> Void) {
        NSAnimationContext.runAnimationGroup { context in
            context.duration = 0.2
            overlay.animator().layer?.opacity = 0
        } completionHandler: {
            completion()
        }
    }
    
    private func showError(_ message: String) {
        let alert = NSAlert()
        alert.messageText = "Ошибка"
        alert.informativeText = message
        alert.addButton(withTitle: "OK")
        alert.runModal()
    }
    
    // MARK: - Stable Button Creation
    
    private func createStableButton(title: String, isPrimary: Bool = false) -> NSButton {
        let button = NSButton(title: title, target: nil, action: nil)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.isBordered = false
        button.wantsLayer = true
        button.isEnabled = true
        button.bezelStyle = .rounded
        
        // Создаем градиентный слой
        let gradientLayer = CAGradientLayer()
        gradientLayer.cornerRadius = 8
        
        if isPrimary {
            // Зеленый градиент для основной кнопки
            gradientLayer.colors = [
                NSColor(red: 0.2, green: 0.8, blue: 0.4, alpha: 1.0).cgColor,
                NSColor(red: 0.1, green: 0.6, blue: 0.3, alpha: 1.0).cgColor
            ]
        } else {
            // Серый градиент для вторичной кнопки
            gradientLayer.colors = [
                NSColor(red: 0.5, green: 0.5, blue: 0.5, alpha: 1.0).cgColor,
                NSColor(red: 0.3, green: 0.3, blue: 0.3, alpha: 1.0).cgColor
            ]
        }
        
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 0, y: 1)
        
        // Добавляем градиент как подслой
        button.layer?.addSublayer(gradientLayer)
        
        // Белый текст
        button.attributedTitle = NSAttributedString(
            string: title,
            attributes: [
                .foregroundColor: NSColor.white,
                .font: NSFont.systemFont(ofSize: 14, weight: .medium)
            ]
        )
        
        // Обновляем размер градиента при изменении размера кнопки
        DispatchQueue.main.async {
            gradientLayer.frame = button.bounds
        }
        
        return button
    }
}


