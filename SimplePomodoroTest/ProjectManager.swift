import Foundation

class ProjectManager: ObservableObject {
    
    // MARK: - Properties
    
    @Published private(set) var projects: [Project] = []
    @Published private(set) var favoriteProjectIds: [UUID] = []
    @Published private(set) var projectOrder: [UUID] = [] // Порядок проектов для drag & drop

    private let userDefaults = UserDefaults.standard
    private let projectsKey = "projects"
    private let favoriteProjectsKey = "favoriteProjects"
    private let projectOrderKey = "projectOrder"
    
    // MARK: - Initialization
    
    init() {
        loadProjects()
        loadFavoriteProjects()
        loadProjectOrder()

        // Создаем проекты по умолчанию если их нет
        if projects.isEmpty {
            createDefaultProjects()
        }

        print("📁 ProjectManager: Инициализирован. Проектов: \(projects.count), Избранных: \(favoriteProjectIds.count), Порядок: \(projectOrder.count)")
    }
    
    // MARK: - CRUD Operations
    
    /// Создает новый проект
    @discardableResult
    func createProject(name: String, type: ProjectType, parentId: UUID? = nil, color: String? = nil, customEmoji: String? = nil) -> Project {
        let project = Project(name: name, type: type, parentId: parentId, color: color, customEmoji: customEmoji)
        projects.append(project)
        saveProjects()

        print("📁 ProjectManager: Создан проект '\(project.name)' типа \(project.type.displayName)")
        return project
    }
    
    /// Обновляет существующий проект
    func updateProject(_ project: Project) {
        if let index = projects.firstIndex(where: { $0.id == project.id }) {
            projects[index] = project
            saveProjects()
            print("📁 ProjectManager: Обновлен проект '\(project.name)'")
        }
    }
    
    /// Удаляет проект
    func deleteProject(_ project: Project) {
        // Удаляем из избранного если есть
        removeFromFavorites(project)

        // Удаляем сам проект
        projects.removeAll { $0.id == project.id }
        saveProjects()

        print("📁 ProjectManager: Удален проект '\(project.name)'")
    }

    /// Удаляет проект по ID
    func deleteProject(_ projectId: UUID) {
        guard let project = projects.first(where: { $0.id == projectId }) else { return }
        deleteProject(project)
    }
    
    /// Получает проект по ID
    func getProject(by id: UUID) -> Project? {
        return projects.first { $0.id == id }
    }
    
    /// Возвращает все проекты (включая архивированные)
    func getAllProjects() -> [Project] {
        return projects
    }

    /// Возвращает все активные проекты
    func getActiveProjects() -> [Project] {
        return projects.filter { $0.isActive && !$0.isArchived }
    }
    
    /// Возвращает все архивированные проекты
    func getArchivedProjects() -> [Project] {
        return projects.filter { $0.isArchived }
    }
    
    /// Возвращает проекты по типу
    func getProjects(by type: ProjectType) -> [Project] {
        return projects.filter { $0.type == type && $0.isActive && !$0.isArchived }
    }

    /// Возвращает рабочие проекты
    func getWorkProjects() -> [Project] {
        return projects.filter { $0.isWorkRelated && $0.isActive && !$0.isArchived }
    }

    /// Возвращает личные проекты
    func getPersonalProjects() -> [Project] {
        return projects.filter { !$0.isWorkRelated && $0.isActive && !$0.isArchived }
    }

    /// Возвращает все проекты, сгруппированные по рабочим/личным
    func getProjectsGroupedByWorkType() -> (work: [Project], personal: [Project]) {
        let activeProjects = getActiveProjects()
        let workProjects = activeProjects.filter { $0.isWorkRelated }
        let personalProjects = activeProjects.filter { !$0.isWorkRelated }
        return (work: workProjects, personal: personalProjects)
    }
    
    // MARK: - Favorites Management
    
    /// Возвращает избранные проекты в порядке приоритета (учитывает projectOrder)
    func getFavoriteProjects() -> [Project] {
        // Получаем проекты в правильном порядке
        let orderedProjects = getProjectsInOrder()

        // Фильтруем только избранные
        return orderedProjects.filter { project in
            favoriteProjectIds.contains(project.id) && project.isActive && !project.isArchived
        }
    }
    
    /// Добавляет проект в избранное
    func addToFavorites(_ project: Project) {
        guard !favoriteProjectIds.contains(project.id) else { return }
        
        // Ограничиваем количество избранных проектов
        if favoriteProjectIds.count >= 5 {
            print("📁 ProjectManager: Достигнуто максимальное количество избранных проектов (5)")
            return
        }
        
        favoriteProjectIds.append(project.id)
        saveFavoriteProjects()
        
        print("📁 ProjectManager: Проект '\(project.name)' добавлен в избранное")
    }
    
    /// Удаляет проект из избранного
    func removeFromFavorites(_ project: Project) {
        favoriteProjectIds.removeAll { $0 == project.id }
        saveFavoriteProjects()
        
        print("📁 ProjectManager: Проект '\(project.name)' удален из избранного")
    }
    
    /// Проверяет, находится ли проект в избранном
    func isFavorite(_ project: Project) -> Bool {
        return favoriteProjectIds.contains(project.id)
    }
    
    /// Изменяет порядок избранных проектов
    func reorderFavorites(from sourceIndex: Int, to destinationIndex: Int) {
        guard sourceIndex < favoriteProjectIds.count && destinationIndex < favoriteProjectIds.count else { return }

        let movedProject = favoriteProjectIds.remove(at: sourceIndex)
        favoriteProjectIds.insert(movedProject, at: destinationIndex)
        saveFavoriteProjects()

        print("📁 ProjectManager: Изменен порядок избранных проектов")
    }

    // MARK: - Project Order Management

    /// Устанавливает новый порядок проектов
    func setProjectOrder(_ orderedProjectIds: [UUID]) {
        projectOrder = orderedProjectIds
        saveProjectOrder()
        print("📁 ProjectManager: Установлен новый порядок проектов: \(orderedProjectIds.count) элементов")
    }

    /// Возвращает проекты в пользовательском порядке
    func getProjectsInOrder() -> [Project] {
        // Если порядок не установлен, возвращаем проекты как есть
        if projectOrder.isEmpty {
            return getAllProjects()
        }

        // Сортируем проекты согласно установленному порядку
        var orderedProjects: [Project] = []

        // Сначала добавляем проекты в установленном порядке
        for projectId in projectOrder {
            if let project = projects.first(where: { $0.id == projectId }) {
                orderedProjects.append(project)
            }
        }

        // Добавляем проекты, которых нет в порядке (новые проекты)
        let orderedIds = Set(projectOrder)
        let newProjects = projects.filter { !orderedIds.contains($0.id) }
        orderedProjects.append(contentsOf: newProjects)

        return orderedProjects
    }
    
    // MARK: - Usage Tracking

    /// Отмечает проект как использованный
    func markProjectAsUsed(_ projectId: UUID) {
        if let index = projects.firstIndex(where: { $0.id == projectId }) {
            projects[index].markAsUsed()
            saveProjects()
        }
    }

    /// Возвращает недавно использованные проекты
    func getRecentProjects(limit: Int = 5) -> [Project] {
        return projects
            .filter { $0.isActive && !$0.isArchived && $0.lastUsedAt != nil }
            .sorted(by: { $0.lastUsedAt! > $1.lastUsedAt! })
            .prefix(limit)
            .map { $0 }
    }
    
    // MARK: - Archive Management
    
    /// Архивирует проект
    func archiveProject(_ project: Project) {
        if let index = projects.firstIndex(where: { $0.id == project.id }) {
            projects[index].archive()
            // Удаляем из избранного при архивировании
            removeFromFavorites(project)
            saveProjects()
            
            print("📁 ProjectManager: Проект '\(project.name)' архивирован")
        }
    }
    
    /// Разархивирует проект
    func unarchiveProject(_ project: Project) {
        if let index = projects.firstIndex(where: { $0.id == project.id }) {
            projects[index].unarchive()
            saveProjects()
            
            print("📁 ProjectManager: Проект '\(project.name)' разархивирован")
        }
    }
    
    // MARK: - Private Methods
    
    private func createDefaultProjects() {
        let defaultProjects = Project.createDefaultProjects()
        projects.append(contentsOf: defaultProjects)

        // Добавляем первые 3 проекта в избранное
        favoriteProjectIds = Array(defaultProjects.prefix(3).map { $0.id })

        saveProjects()
        saveFavoriteProjects()

        print("📁 ProjectManager: Созданы проекты по умолчанию")
    }

    // MARK: - Data Persistence

    private func saveProjects() {
        do {
            let data = try JSONEncoder().encode(projects)
            userDefaults.set(data, forKey: projectsKey)
            print("📁 ProjectManager: Проекты сохранены")
        } catch {
            print("❌ ProjectManager: Ошибка сохранения проектов: \(error)")
        }
    }

    private func loadProjects() {
        guard let data = userDefaults.data(forKey: projectsKey) else {
            print("📁 ProjectManager: Данные проектов не найдены")
            return
        }

        do {
            projects = try JSONDecoder().decode([Project].self, from: data)
            print("📁 ProjectManager: Загружено проектов: \(projects.count)")
        } catch {
            print("❌ ProjectManager: Ошибка загрузки проектов: \(error)")
            projects = []
        }
    }

    private func saveFavoriteProjects() {
        do {
            let data = try JSONEncoder().encode(favoriteProjectIds)
            userDefaults.set(data, forKey: favoriteProjectsKey)
            print("📁 ProjectManager: Избранные проекты сохранены")
        } catch {
            print("❌ ProjectManager: Ошибка сохранения избранных проектов: \(error)")
        }
    }

    private func loadFavoriteProjects() {
        guard let data = userDefaults.data(forKey: favoriteProjectsKey) else {
            print("📁 ProjectManager: Данные избранных проектов не найдены")
            return
        }

        do {
            favoriteProjectIds = try JSONDecoder().decode([UUID].self, from: data)
            print("📁 ProjectManager: Загружено избранных проектов: \(favoriteProjectIds.count)")
        } catch {
            print("❌ ProjectManager: Ошибка загрузки избранных проектов: \(error)")
            favoriteProjectIds = []
        }
    }

    private func saveProjectOrder() {
        do {
            let data = try JSONEncoder().encode(projectOrder)
            userDefaults.set(data, forKey: projectOrderKey)
            print("📁 ProjectManager: Порядок проектов сохранен")
        } catch {
            print("❌ ProjectManager: Ошибка сохранения порядка проектов: \(error)")
        }
    }

    private func loadProjectOrder() {
        guard let data = userDefaults.data(forKey: projectOrderKey) else {
            print("📁 ProjectManager: Данные порядка проектов не найдены")
            return
        }

        do {
            projectOrder = try JSONDecoder().decode([UUID].self, from: data)
            print("📁 ProjectManager: Загружен порядок проектов: \(projectOrder.count)")
        } catch {
            print("❌ ProjectManager: Ошибка загрузки порядка проектов: \(error)")
            projectOrder = []
        }
    }

    // MARK: - Data Management

    /// Очищает все данные проектов (для тестирования)
    func clearAllData() {
        projects.removeAll()
        favoriteProjectIds.removeAll()
        projectOrder.removeAll()

        userDefaults.removeObject(forKey: projectsKey)
        userDefaults.removeObject(forKey: favoriteProjectsKey)
        userDefaults.removeObject(forKey: projectOrderKey)

        print("📁 ProjectManager: Все данные очищены")
    }
}
