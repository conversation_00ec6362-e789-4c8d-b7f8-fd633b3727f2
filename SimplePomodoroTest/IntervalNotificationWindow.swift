import Cocoa
import QuartzCore

// Расширение для NSBezierPath
extension NSBezierPath {
    var cgPath: CGPath {
        let path = CGMutablePath()
        var points = [CGPoint](repeating: .zero, count: 3)

        for i in 0..<elementCount {
            let type = element(at: i, associatedPoints: &points)
            switch type {
            case .moveTo:
                path.move(to: points[0])
            case .lineTo:
                path.addLine(to: points[0])
            case .curveTo:
                path.addCurve(to: points[2], control1: points[0], control2: points[1])
            case .cubicCurveTo:
                path.addCurve(to: points[2], control1: points[0], control2: points[1])
            case .quadraticCurveTo:
                path.addQuadCurve(to: points[1], control: points[0])
            case .closePath:
                path.closeSubpath()
            @unknown default:
                break
            }
        }
        return path
    }
}

class IntervalNotificationWindow: NSWindow {
    
    private var reminderCount: Int = 0
    private var onComplete: (() -> Void)?
    private var onExtend1: (() -> Void)?
    private var onExtend5: (() -> Void)?
    private var messageLabel: NSTextField!
    
    init() {
        // Создаем компактное окно
        let windowRect = NSRect(x: 0, y: 0, width: 320, height: 120)
        
        super.init(
            contentRect: windowRect,
            styleMask: [.borderless],
            backing: .buffered,
            defer: false
        )
        
        print("🎨 IntervalNotificationWindow: Инициализация красивого окна")
        setupWindow()
        setupUI()
        positionWindow()
    }
    
    private func setupWindow() {
        print("🎨 IntervalNotificationWindow: Настройка окна")
        
        // Настройки окна
        self.level = .floating
        self.isOpaque = false
        self.backgroundColor = NSColor.clear
        self.ignoresMouseEvents = false
        self.hasShadow = true
        
        // Показываем на всех рабочих столах
        self.collectionBehavior = [.canJoinAllSpaces, .fullScreenAuxiliary]
    }
    
    private func positionWindow() {
        // Позиционируем окно относительно status bar item
        positionRelativeToStatusItem()
    }

    func positionRelativeToStatusItem() {
        // Получаем позицию status bar через глобальные координаты
        if let screen = NSScreen.main {
            let screenFrame = screen.frame
            let windowFrame = self.frame

            // Позиционируем в правом верхнем углу, чуть ниже menu bar
            let x = screenFrame.maxX - windowFrame.width - 10
            let y = screenFrame.maxY - windowFrame.height - 30 // Отступ от menu bar

            self.setFrameOrigin(NSPoint(x: x, y: y))
        }
    }

    func positionRelativeToStatusItem(statusItemFrame: NSRect) {
        let windowFrame = self.frame

        // Позиционируем окно прямо под status item
        let x = statusItemFrame.midX - windowFrame.width / 2
        let y = statusItemFrame.minY - windowFrame.height - 5

        self.setFrameOrigin(NSPoint(x: x, y: y))
    }
    
    private func setupUI() {
        print("🎨 IntervalNotificationWindow: Настройка красивого UI")
        
        let contentView = NSView(frame: self.contentRect(forFrameRect: self.frame))
        self.contentView = contentView

        // Visual effect view для glassmorphism размытия
        let visualEffectView = NSVisualEffectView()
        visualEffectView.material = .hudWindow
        visualEffectView.blendingMode = .behindWindow
        visualEffectView.state = .active
        visualEffectView.wantsLayer = true
        visualEffectView.layer?.cornerRadius = 12
        visualEffectView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(visualEffectView)

        // Основной контейнер с градиентом поверх размытия
        let containerView = GradientView()
        containerView.translatesAutoresizingMaskIntoConstraints = false
        visualEffectView.addSubview(containerView)
        
        // Красивая зеленая галочка
        let checkmarkView = createCheckmarkView()
        checkmarkView.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(checkmarkView)
        
        // Сообщение
        messageLabel = NSTextField(labelWithString: getMessageText())
        messageLabel.font = NSFont.systemFont(ofSize: 12, weight: .medium) // Уменьшили шрифт
        messageLabel.textColor = NSColor.white
        messageLabel.alignment = .left
        messageLabel.maximumNumberOfLines = 2
        messageLabel.preferredMaxLayoutWidth = 200
        messageLabel.translatesAutoresizingMaskIntoConstraints = false
        messageLabel.backgroundColor = NSColor.clear
        messageLabel.isBordered = false
        containerView.addSubview(messageLabel)
        
        // Кнопки
        // КРАСИВЫЕ КНОПКИ БЕЗ КАСТОМНОГО КЛАССА
        let extend1Button = createStyledButton(title: "+1 мин", isGreen: false, isSmall: true)
        extend1Button.target = self
        extend1Button.action = #selector(extend1ButtonClicked)

        let extend5Button = createStyledButton(title: "+5 мин", isGreen: false, isSmall: true)
        extend5Button.target = self
        extend5Button.action = #selector(extend5ButtonClicked)

        let completeButton = createStyledButton(title: "Завершить", isGreen: true, isSmall: false)
        completeButton.target = self
        completeButton.action = #selector(completeButtonClicked)

        containerView.addSubview(extend1Button)
        containerView.addSubview(extend5Button)
        containerView.addSubview(completeButton)
        
        // Constraints
        NSLayoutConstraint.activate([
            // Visual effect view заполняет все окно
            visualEffectView.topAnchor.constraint(equalTo: contentView.topAnchor),
            visualEffectView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            visualEffectView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            visualEffectView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),

            // Container заполняет visual effect view
            containerView.topAnchor.constraint(equalTo: visualEffectView.topAnchor),
            containerView.leadingAnchor.constraint(equalTo: visualEffectView.leadingAnchor),
            containerView.trailingAnchor.constraint(equalTo: visualEffectView.trailingAnchor),
            containerView.bottomAnchor.constraint(equalTo: visualEffectView.bottomAnchor),
            
            // Галочка слева
            checkmarkView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 15),
            checkmarkView.centerYAnchor.constraint(equalTo: containerView.centerYAnchor),
            checkmarkView.widthAnchor.constraint(equalToConstant: 30),
            checkmarkView.heightAnchor.constraint(equalToConstant: 30),

            // Сообщение слева по центру
            messageLabel.leadingAnchor.constraint(equalTo: checkmarkView.trailingAnchor, constant: 10),
            messageLabel.centerYAnchor.constraint(equalTo: containerView.centerYAnchor),
            messageLabel.trailingAnchor.constraint(equalTo: extend1Button.leadingAnchor, constant: -15),

            // Кнопки справа: +1 мин и +5 мин рядом (компактнее)
            extend1Button.trailingAnchor.constraint(equalTo: extend5Button.leadingAnchor, constant: -6),
            extend1Button.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 15),
            extend1Button.widthAnchor.constraint(equalToConstant: 60), // Уменьшили ширину
            extend1Button.heightAnchor.constraint(equalToConstant: 24), // Уменьшили высоту

            extend5Button.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -15),
            extend5Button.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 15),
            extend5Button.widthAnchor.constraint(equalToConstant: 60), // Уменьшили ширину
            extend5Button.heightAnchor.constraint(equalToConstant: 24), // Уменьшили высоту

            // Кнопка "Завершить" под ними по центру (компактнее)
            completeButton.centerXAnchor.constraint(equalTo: extend1Button.centerXAnchor, constant: 33), // Центр между двумя кнопками
            completeButton.topAnchor.constraint(equalTo: extend1Button.bottomAnchor, constant: 8),
            completeButton.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -15), // Равный отступ снизу
            completeButton.widthAnchor.constraint(equalToConstant: 126), // Ширина обеих кнопок + отступ
            completeButton.heightAnchor.constraint(equalToConstant: 28) // Уменьшили высоту
        ])
        
        print("🎨 IntervalNotificationWindow: Красивый UI настроен")
    }

    private func createStyledButton(title: String, isGreen: Bool, isSmall: Bool) -> NSButton {
        let button = NSButton(title: title, target: nil, action: nil)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.wantsLayer = true

        // Настройка размера и шрифта
        if isSmall {
            button.controlSize = .small
            button.font = NSFont.systemFont(ofSize: 11, weight: .medium)
        } else {
            button.controlSize = .regular
            button.font = NSFont.systemFont(ofSize: 13, weight: .semibold)
        }

        // Настройка цветов
        if isGreen {
            // Зеленая кнопка "Завершить"
            button.bezelStyle = .rounded
            button.contentTintColor = NSColor.systemGreen

            // Дополнительная стилизация для зеленой кнопки
            button.layer?.backgroundColor = NSColor.systemGreen.withAlphaComponent(0.1).cgColor
            button.layer?.borderColor = NSColor.systemGreen.cgColor
            button.layer?.borderWidth = 1.0
            button.layer?.cornerRadius = 6.0
        } else {
            // Серые кнопки "+1 мин", "+5 мин"
            button.bezelStyle = .rounded
            button.contentTintColor = NSColor.controlAccentColor

            // Дополнительная стилизация для серых кнопок
            button.layer?.backgroundColor = NSColor.controlAccentColor.withAlphaComponent(0.1).cgColor
            button.layer?.borderColor = NSColor.controlAccentColor.cgColor
            button.layer?.borderWidth = 1.0
            button.layer?.cornerRadius = 6.0
        }

        return button
    }


    

    
    private func getMessageText() -> String {
        switch reminderCount {
        case 0:
            return "Интервал завершен!\nВремя для отдыха 🌟"
        case 1:
            return "Пожалуйста, сделайте\nперерыв 😊"
        case 2:
            return "Серьезно, пора\nотдохнуть! 😐"
        case 3:
            return "⚠️ ВНИМАНИЕ!\nВыгорание близко!"
        case 4:
            return "🚨 КРИТИЧНО!\nНемедленно отдохните!"
        default:
            return "🔥 ОПАСНОСТЬ!\nВаше здоровье важнее!"
        }
    }
    
    func updateMessage(reminderCount: Int) {
        print("🎨 IntervalNotificationWindow: Обновление сообщения, напоминание #\(reminderCount)")
        self.reminderCount = reminderCount
        
        messageLabel.stringValue = getMessageText()
        
        // Добавляем пульсацию для критических сообщений
        if reminderCount >= 3 {
            addPulseAnimation()
        }
    }
    
    private func addPulseAnimation() {
        messageLabel.wantsLayer = true
        let animation = CABasicAnimation(keyPath: "transform.scale")
        animation.fromValue = 1.0
        animation.toValue = 1.05
        animation.duration = 0.6
        animation.autoreverses = true
        animation.repeatCount = .infinity
        messageLabel.layer?.add(animation, forKey: "pulse")
    }
    
    func setCallbacks(onComplete: @escaping () -> Void, onExtend1: @escaping () -> Void, onExtend5: @escaping () -> Void) {
        self.onComplete = onComplete
        self.onExtend1 = onExtend1
        self.onExtend5 = onExtend5
    }
    
    @objc private func completeButtonClicked() {
        print("🎨 IntervalNotificationWindow: Нажата кнопка 'Завершить'")
        hideWindow()
        onComplete?()
    }

    @objc private func extend1ButtonClicked() {
        print("🎨 IntervalNotificationWindow: Нажата кнопка '+1 мин'")
        hideWindow()
        // Продлеваем на 1 минуту
        onExtend1?()
    }

    @objc private func extend5ButtonClicked() {
        print("🎨 IntervalNotificationWindow: Нажата кнопка '+5 мин'")
        hideWindow()
        // Продлеваем на 5 минут
        onExtend5?()
    }
    
    private func hideWindow() {
        print("🎨 IntervalNotificationWindow: Скрытие окна с красивой анимацией")

        // Красивая анимация скрытия
        NSAnimationContext.runAnimationGroup({ context in
            context.duration = 0.3
            context.timingFunction = CAMediaTimingFunction(name: .easeIn)

            // Растворение
            self.animator().alphaValue = 0

            // Легкое уменьшение для красоты
            if let contentView = self.contentView {
                contentView.wantsLayer = true
                let transform = CATransform3DMakeScale(0.9, 0.9, 1)
                contentView.layer?.transform = transform
            }
        }) {
            self.orderOut(nil)

            // Возвращаем исходное состояние для следующего показа
            if let contentView = self.contentView {
                contentView.layer?.transform = CATransform3DIdentity
            }
            self.alphaValue = 1
        }
    }
    
    func showWindow() {
        print("🎨 IntervalNotificationWindow: Показ окна с анимацией")
        
        // Начинаем с прозрачности
        self.alphaValue = 0
        self.makeKeyAndOrderFront(nil)
        
        // Анимация появления
        NSAnimationContext.runAnimationGroup({ context in
            context.duration = 0.4
            context.timingFunction = CAMediaTimingFunction(name: .easeOut)
            self.animator().alphaValue = 1
        })
        
        NSApp.activate(ignoringOtherApps: true)
    }
    
    override var canBecomeKey: Bool {
        return true
    }
    
    override var canBecomeMain: Bool {
        return true
    }

    // MARK: - Красивая галочка

    private func createCheckmarkView() -> NSView {
        let view = NSView()
        view.wantsLayer = true

        // Создаем слой для галочки
        let checkmarkLayer = CAShapeLayer()

        // Создаем путь для галочки
        let checkmarkPath = NSBezierPath()

        // Рисуем галочку (размер 30x30)
        let size: CGFloat = 30
        let strokeWidth: CGFloat = 3

        // Начинаем с левой части галочки
        checkmarkPath.move(to: NSPoint(x: size * 0.2, y: size * 0.5))
        // Идем к центру (нижняя точка)
        checkmarkPath.line(to: NSPoint(x: size * 0.45, y: size * 0.25))
        // Идем к правой части (верхняя точка)
        checkmarkPath.line(to: NSPoint(x: size * 0.8, y: size * 0.75))

        // Настраиваем слой
        checkmarkLayer.path = checkmarkPath.cgPath
        checkmarkLayer.fillColor = NSColor.clear.cgColor
        checkmarkLayer.strokeColor = NSColor.systemGreen.cgColor
        checkmarkLayer.lineWidth = strokeWidth
        checkmarkLayer.lineCap = .round
        checkmarkLayer.lineJoin = .round

        // Добавляем круглый фон
        let backgroundLayer = CAShapeLayer()
        let backgroundPath = NSBezierPath(ovalIn: NSRect(x: 0, y: 0, width: size, height: size))
        backgroundLayer.path = backgroundPath.cgPath
        backgroundLayer.fillColor = NSColor.systemGreen.withAlphaComponent(0.1).cgColor
        backgroundLayer.strokeColor = NSColor.systemGreen.cgColor
        backgroundLayer.lineWidth = 2

        // Добавляем слои к view
        view.layer?.addSublayer(backgroundLayer)
        view.layer?.addSublayer(checkmarkLayer)

        return view
    }
}
