import Cocoa

protocol ProjectCreateFormDelegate: AnyObject {
    func projectCreateFormDidCreateProject(_ project: Project)
    func projectCreateFormDidCancel()
}

class ProjectCreateForm: NSView {
    
    // MARK: - Properties
    
    weak var delegate: ProjectCreateFormDelegate?
    private let projectManager: ProjectManager
    
    private var containerView: NSView!
    private var titleLabel: NSTextField!
    private var nameField: NSTextField!
    private var typePopup: NSPopUpButton!
    private var colorWell: NSColorWell!
    private var emojiField: NSTextField!
    private var emojiButtonsContainer: NSView!
    private var createButton: NSButton!
    private var cancelButton: NSButton!
    
    private var emojiButtons: [NSButton] = []
    private let popularEmojis = ["📝", "💻", "🎯", "📊", "🎨", "🔬", "📚", "🏃‍♂️", "🎵", "🍳"]
    
    // MARK: - Initialization
    
    init(projectManager: ProjectManager) {
        self.projectManager = projectManager
        super.init(frame: .zero)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        wantsLayer = true
        setupBackground()
        setupContent()
        setupConstraints()
    }
    
    private func setupBackground() {
        // Создаем glassmorphism фон
        let gradientLayer = CAGradientLayer()
        
        let color1 = NSColor(red: 0.1, green: 0.1, blue: 0.15, alpha: 0.95)
        let color2 = NSColor(red: 0.15, green: 0.1, blue: 0.2, alpha: 0.9)
        let color3 = NSColor(red: 0.1, green: 0.15, blue: 0.2, alpha: 0.95)
        
        gradientLayer.colors = [color1.cgColor, color2.cgColor, color3.cgColor]
        gradientLayer.locations = [0.0, 0.5, 1.0]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 1)
        gradientLayer.cornerRadius = 16
        
        gradientLayer.borderColor = NSColor.white.withAlphaComponent(0.2).cgColor
        gradientLayer.borderWidth = 1
        
        gradientLayer.shadowColor = NSColor.black.cgColor
        gradientLayer.shadowOpacity = 0.4
        gradientLayer.shadowOffset = CGSize(width: 0, height: 8)
        gradientLayer.shadowRadius = 24
        
        layer = gradientLayer
    }
    
    private func setupContent() {
        containerView = NSView()
        containerView.translatesAutoresizingMaskIntoConstraints = false
        addSubview(containerView)
        
        // Заголовок
        titleLabel = NSTextField(labelWithString: "Создать новый проект")
        titleLabel.font = NSFont.systemFont(ofSize: 24, weight: .bold)
        titleLabel.textColor = NSColor.white
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(titleLabel)
        
        // Поле названия
        let nameLabel = NSTextField(labelWithString: "Название проекта:")
        nameLabel.font = NSFont.systemFont(ofSize: 14, weight: .medium)
        nameLabel.textColor = NSColor.white.withAlphaComponent(0.8)
        nameLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(nameLabel)
        
        nameField = NSTextField()
        nameField.placeholderString = "Введите название проекта"
        nameField.translatesAutoresizingMaskIntoConstraints = false
        setupTextFieldStyle(nameField)
        containerView.addSubview(nameField)
        
        // Тип проекта
        let typeLabel = NSTextField(labelWithString: "Тип проекта:")
        typeLabel.font = NSFont.systemFont(ofSize: 14, weight: .medium)
        typeLabel.textColor = NSColor.white.withAlphaComponent(0.8)
        typeLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(typeLabel)
        
        typePopup = NSPopUpButton()
        typePopup.translatesAutoresizingMaskIntoConstraints = false
        setupPopupStyle(typePopup)
        
        for projectType in ProjectType.allCases {
            typePopup.addItem(withTitle: "\(projectType.emoji) \(projectType.displayName)")
        }
        containerView.addSubview(typePopup)
        
        // Цвет проекта
        let colorLabel = NSTextField(labelWithString: "Цвет проекта:")
        colorLabel.font = NSFont.systemFont(ofSize: 14, weight: .medium)
        colorLabel.textColor = NSColor.white.withAlphaComponent(0.8)
        colorLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(colorLabel)
        
        colorWell = NSColorWell()
        colorWell.color = NSColor.systemBlue
        colorWell.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(colorWell)
        
        // Кастомный эмодзи
        let emojiLabel = NSTextField(labelWithString: "Эмодзи (необязательно):")
        emojiLabel.font = NSFont.systemFont(ofSize: 14, weight: .medium)
        emojiLabel.textColor = NSColor.white.withAlphaComponent(0.8)
        emojiLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(emojiLabel)
        
        emojiField = NSTextField()
        emojiField.placeholderString = "Или выберите ниже"
        emojiField.translatesAutoresizingMaskIntoConstraints = false
        setupTextFieldStyle(emojiField)
        containerView.addSubview(emojiField)
        
        // Популярные эмодзи
        setupEmojiButtons()
        
        // Кнопки
        let buttonsContainer = NSView()
        buttonsContainer.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(buttonsContainer)
        
        cancelButton = createStableButton(title: "Отмена", isPrimary: false)
        cancelButton.target = self
        cancelButton.action = #selector(cancelClicked)
        buttonsContainer.addSubview(cancelButton)

        createButton = createStableButton(title: "Создать проект", isPrimary: true)
        createButton.target = self
        createButton.action = #selector(createClicked)
        buttonsContainer.addSubview(createButton)
        
        NSLayoutConstraint.activate([
            cancelButton.leadingAnchor.constraint(equalTo: buttonsContainer.leadingAnchor),
            cancelButton.centerYAnchor.constraint(equalTo: buttonsContainer.centerYAnchor),
            cancelButton.widthAnchor.constraint(equalToConstant: 120),
            cancelButton.heightAnchor.constraint(equalToConstant: 36),
            
            createButton.trailingAnchor.constraint(equalTo: buttonsContainer.trailingAnchor),
            createButton.centerYAnchor.constraint(equalTo: buttonsContainer.centerYAnchor),
            createButton.widthAnchor.constraint(equalToConstant: 160),
            createButton.heightAnchor.constraint(equalToConstant: 36),
            
            buttonsContainer.heightAnchor.constraint(equalToConstant: 36)
        ])
    }
    
    private func setupTextFieldStyle(_ textField: NSTextField) {
        textField.wantsLayer = true
        textField.layer?.backgroundColor = NSColor.black.withAlphaComponent(0.3).cgColor
        textField.layer?.cornerRadius = 8
        textField.layer?.borderColor = NSColor.white.withAlphaComponent(0.2).cgColor
        textField.layer?.borderWidth = 1
        textField.textColor = NSColor.white
        textField.font = NSFont.systemFont(ofSize: 14)
        
        if let cell = textField.cell as? NSTextFieldCell {
            cell.placeholderAttributedString = NSAttributedString(
                string: textField.placeholderString ?? "",
                attributes: [
                    .foregroundColor: NSColor.white.withAlphaComponent(0.5),
                    .font: NSFont.systemFont(ofSize: 14)
                ]
            )
        }
    }
    
    private func setupPopupStyle(_ popup: NSPopUpButton) {
        popup.wantsLayer = true
        popup.layer?.backgroundColor = NSColor.black.withAlphaComponent(0.3).cgColor
        popup.layer?.cornerRadius = 8
        popup.layer?.borderColor = NSColor.white.withAlphaComponent(0.2).cgColor
        popup.layer?.borderWidth = 1
    }
    
    private func setupEmojiButtons() {
        emojiButtonsContainer = NSView()
        emojiButtonsContainer.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(emojiButtonsContainer)
        
        for (index, emoji) in popularEmojis.enumerated() {
            let button = NSButton(title: emoji, target: self, action: #selector(emojiButtonClicked(_:)))
            button.isBordered = false
            button.wantsLayer = true
            button.layer?.backgroundColor = NSColor.black.withAlphaComponent(0.2).cgColor
            button.layer?.cornerRadius = 6
            button.layer?.borderColor = NSColor.white.withAlphaComponent(0.1).cgColor
            button.layer?.borderWidth = 1
            button.font = NSFont.systemFont(ofSize: 18)
            button.translatesAutoresizingMaskIntoConstraints = false
            emojiButtonsContainer.addSubview(button)
            emojiButtons.append(button)
            
            let row = index / 5
            let col = index % 5
            
            NSLayoutConstraint.activate([
                button.widthAnchor.constraint(equalToConstant: 40),
                button.heightAnchor.constraint(equalToConstant: 40),
                button.leadingAnchor.constraint(equalTo: emojiButtonsContainer.leadingAnchor, constant: CGFloat(col * 50)),
                button.topAnchor.constraint(equalTo: emojiButtonsContainer.topAnchor, constant: CGFloat(row * 50))
            ])
        }
    }
    
    private func setupConstraints() {
        // Найдем все элементы для constraints
        let nameLabel = containerView.subviews.first { ($0 as? NSTextField)?.stringValue == "Название проекта:" } as? NSTextField
        let typeLabel = containerView.subviews.first { ($0 as? NSTextField)?.stringValue == "Тип проекта:" } as? NSTextField
        let colorLabel = containerView.subviews.first { ($0 as? NSTextField)?.stringValue == "Цвет проекта:" } as? NSTextField
        let emojiLabel = containerView.subviews.first { ($0 as? NSTextField)?.stringValue == "Эмодзи (необязательно):" } as? NSTextField
        let buttonsContainer = containerView.subviews.first { $0.subviews.contains(createButton) }

        NSLayoutConstraint.activate([
            containerView.topAnchor.constraint(equalTo: topAnchor, constant: 30),
            containerView.leadingAnchor.constraint(equalTo: leadingAnchor, constant: 30),
            containerView.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -30),
            containerView.bottomAnchor.constraint(equalTo: bottomAnchor, constant: -30),

            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor),
            titleLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor)
        ])

        // Constraints для полей (если найдены)
        if let nameLabel = nameLabel {
            NSLayoutConstraint.activate([
                nameLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 30),
                nameLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),

                nameField.topAnchor.constraint(equalTo: nameLabel.bottomAnchor, constant: 8),
                nameField.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
                nameField.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
                nameField.heightAnchor.constraint(equalToConstant: 36)
            ])
        }

        if let typeLabel = typeLabel {
            NSLayoutConstraint.activate([
                typeLabel.topAnchor.constraint(equalTo: nameField.bottomAnchor, constant: 20),
                typeLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),

                typePopup.topAnchor.constraint(equalTo: typeLabel.bottomAnchor, constant: 8),
                typePopup.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
                typePopup.widthAnchor.constraint(equalToConstant: 200),
                typePopup.heightAnchor.constraint(equalToConstant: 36)
            ])
        }

        if let colorLabel = colorLabel {
            NSLayoutConstraint.activate([
                colorLabel.topAnchor.constraint(equalTo: typePopup.bottomAnchor, constant: 20),
                colorLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),

                colorWell.topAnchor.constraint(equalTo: colorLabel.bottomAnchor, constant: 8),
                colorWell.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
                colorWell.widthAnchor.constraint(equalToConstant: 60),
                colorWell.heightAnchor.constraint(equalToConstant: 36)
            ])
        }

        if let emojiLabel = emojiLabel {
            NSLayoutConstraint.activate([
                emojiLabel.topAnchor.constraint(equalTo: colorWell.bottomAnchor, constant: 20),
                emojiLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),

                emojiField.topAnchor.constraint(equalTo: emojiLabel.bottomAnchor, constant: 8),
                emojiField.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
                emojiField.widthAnchor.constraint(equalToConstant: 200),
                emojiField.heightAnchor.constraint(equalToConstant: 36),

                emojiButtonsContainer.topAnchor.constraint(equalTo: emojiField.bottomAnchor, constant: 16),
                emojiButtonsContainer.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
                emojiButtonsContainer.widthAnchor.constraint(equalToConstant: 250),
                emojiButtonsContainer.heightAnchor.constraint(equalToConstant: 100)
            ])
        }

        if let buttonsContainer = buttonsContainer {
            NSLayoutConstraint.activate([
                buttonsContainer.topAnchor.constraint(equalTo: emojiButtonsContainer.bottomAnchor, constant: 30),
                buttonsContainer.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
                buttonsContainer.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
                buttonsContainer.bottomAnchor.constraint(equalTo: containerView.bottomAnchor)
            ])
        }
    }
    
    // MARK: - Actions
    
    @objc private func emojiButtonClicked(_ sender: NSButton) {
        emojiField.stringValue = sender.title
        print("🎭 Выбран эмодзи: \(sender.title)")
        NSLog("🎭 Выбран эмодзи: \(sender.title)")

        // Легкая анимация без изменения размера
        NSAnimationContext.runAnimationGroup { context in
            context.duration = 0.1
            sender.animator().alphaValue = 0.7
        } completionHandler: {
            NSAnimationContext.runAnimationGroup { context in
                context.duration = 0.1
                sender.animator().alphaValue = 1.0
            }
        }
    }
    
    @objc private func createClicked() {
        print("🎯 Создание проекта - начало")
        NSLog("🎯 Создание проекта - начало")

        let nameValue = nameField.stringValue.trimmingCharacters(in: .whitespacesAndNewlines)
        print("🎯 Название проекта: '\(nameValue)'")
        NSLog("🎯 Название проекта: '\(nameValue)'")

        guard !nameValue.isEmpty else {
            print("🎯 Ошибка: пустое название")
            NSLog("🎯 Ошибка: пустое название")
            showAlert(title: "Ошибка", message: "Введите название проекта")
            return
        }

        let name = nameField.stringValue.trimmingCharacters(in: .whitespacesAndNewlines)
        let selectedTypeIndex = typePopup.indexOfSelectedItem
        let projectType = ProjectType.allCases[selectedTypeIndex]
        let color = colorWell.color.hexString
        let customEmoji = emojiField.stringValue.trimmingCharacters(in: .whitespacesAndNewlines)
        let finalEmoji = customEmoji.isEmpty ? nil : customEmoji

        print("🎯 Создаем проект: \(name)")
        NSLog("🎯 Создаем проект: \(name)")

        let newProject = projectManager.createProject(
            name: name,
            type: projectType,
            color: color,
            customEmoji: finalEmoji
        )

        print("🎯 Проект создан: \(newProject.name)")
        NSLog("🎯 Проект создан: \(newProject.name)")

        delegate?.projectCreateFormDidCreateProject(newProject)
        delegate?.projectCreateFormDidCancel() // Закрываем форму
    }
    
    @objc private func cancelClicked() {
        delegate?.projectCreateFormDidCancel()
    }
    
    private func showAlert(title: String, message: String) {
        let alert = NSAlert()
        alert.messageText = title
        alert.informativeText = message
        alert.addButton(withTitle: "OK")
        alert.runModal()
    }
    
    override func layout() {
        super.layout()
        layer?.frame = bounds
    }

    // MARK: - Stable Button Creation

    private func createStableButton(title: String, isPrimary: Bool = false) -> NSButton {
        let button = NSButton(title: title, target: nil, action: nil)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.isBordered = false
        button.wantsLayer = true
        button.isEnabled = true
        button.bezelStyle = .rounded

        // Создаем градиентный слой
        let gradientLayer = CAGradientLayer()
        gradientLayer.cornerRadius = 8

        if isPrimary {
            // Зеленый градиент для основной кнопки
            gradientLayer.colors = [
                NSColor(red: 0.2, green: 0.8, blue: 0.4, alpha: 1.0).cgColor,
                NSColor(red: 0.1, green: 0.6, blue: 0.3, alpha: 1.0).cgColor
            ]
        } else {
            // Серый градиент для вторичной кнопки
            gradientLayer.colors = [
                NSColor(red: 0.5, green: 0.5, blue: 0.5, alpha: 1.0).cgColor,
                NSColor(red: 0.3, green: 0.3, blue: 0.3, alpha: 1.0).cgColor
            ]
        }

        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 0, y: 1)

        // Добавляем градиент как подслой, а не заменяем основной слой
        button.layer?.addSublayer(gradientLayer)

        // Белый текст
        button.attributedTitle = NSAttributedString(
            string: title,
            attributes: [
                .foregroundColor: NSColor.white,
                .font: NSFont.systemFont(ofSize: 14, weight: .medium)
            ]
        )

        // Обновляем размер градиента при изменении размера кнопки
        DispatchQueue.main.async {
            gradientLayer.frame = button.bounds
        }

        return button
    }
}
