import Foundation

/// Простые тесты для проверки функциональности таймера отдыха
class BreakTimerTests {
    
    static func runAllTests() {
        print("🧪 Запуск тестов таймера отдыха...")
        
        testBreakTimerCreation()
        testBreakTimerStartStop()
        testStatisticsManagerBreaks()
        testPomodoroTimerIntegration()
        
        print("✅ Все тесты завершены!")
    }
    
    static func testBreakTimerCreation() {
        print("🧪 Тест создания BreakTimer...")
        
        let breakTimer = BreakTimer()
        assert(!breakTimer.isActive, "BreakTimer должен быть неактивным при создании")
        assert(breakTimer.timeRemaining == BreakTimer.defaultBreakDuration, "Время должно быть установлено по умолчанию")
        assert(!breakTimer.wasComputerActiveThisBreak, "Активность компьютера должна быть false")
        
        print("✅ Тест создания BreakTimer пройден")
    }
    
    static func testBreakTimerStartStop() {
        print("🧪 Тест запуска и остановки BreakTimer...")
        
        let breakTimer = BreakTimer()
        
        // Тест запуска
        breakTimer.startBreak(duration: 5) // 5 секунд для теста
        assert(breakTimer.isActive, "BreakTimer должен быть активным после запуска")
        assert(breakTimer.timeRemaining == 5, "Время должно быть установлено в 5 секунд")
        
        // Тест остановки
        breakTimer.stopBreak()
        assert(!breakTimer.isActive, "BreakTimer должен быть неактивным после остановки")
        
        print("✅ Тест запуска и остановки BreakTimer пройден")
    }
    
    static func testStatisticsManagerBreaks() {
        print("🧪 Тест StatisticsManager для отдыхов...")
        
        let statsManager = StatisticsManager()
        
        // Записываем тестовый отдых
        statsManager.recordCompletedBreak(
            duration: 15 * 60, // 15 минут
            wasComputerActive: false,
            computerActiveTime: 0
        )
        
        // Проверяем, что отдых записался
        let breaksToday = statsManager.getBreaksForToday()
        assert(breaksToday >= 1, "Должен быть записан хотя бы один отдых")
        
        // Проверяем качество отдыха
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let tomorrow = calendar.date(byAdding: .day, value: 1, to: today)!
        let quality = statsManager.getBreakQualityForDateRange(from: today, to: tomorrow)
        
        assert(quality.total >= 1, "Должен быть хотя бы один отдых в статистике")
        
        print("✅ Тест StatisticsManager для отдыхов пройден")
    }
    
    static func testPomodoroTimerIntegration() {
        print("🧪 Тест интеграции PomodoroTimer с BreakTimer...")
        
        let pomodoroTimer = PomodoroTimer()
        
        // Проверяем начальное состояние
        assert(pomodoroTimer.state == .idle, "PomodoroTimer должен быть в состоянии idle")
        assert(pomodoroTimer.breakTimeRemaining == 0, "Время отдыха должно быть 0")
        
        // Тестируем переход в состояние отдыха
        pomodoroTimer.startBreak()
        assert(pomodoroTimer.state == .onBreak, "PomodoroTimer должен быть в состоянии onBreak")
        assert(pomodoroTimer.breakTimeRemaining > 0, "Время отдыха должно быть больше 0")
        
        // Тестируем остановку отдыха
        pomodoroTimer.stopBreak()
        assert(pomodoroTimer.state == .idle, "PomodoroTimer должен вернуться в состояние idle")
        
        print("✅ Тест интеграции PomodoroTimer с BreakTimer пройден")
    }
    
    static func testBreakStatistics() {
        print("🧪 Тест статистики отдыха...")
        
        let breakTimer = BreakTimer()
        
        // Запускаем отдых
        breakTimer.startBreak(duration: 10) // 10 секунд для теста
        
        // Получаем статистику
        let stats = breakTimer.getBreakStatistics()
        assert(stats != nil, "Статистика должна быть доступна во время отдыха")
        assert(stats!.duration == 10, "Продолжительность должна быть 10 секунд")
        assert(stats!.progress >= 0 && stats!.progress <= 1, "Прогресс должен быть от 0 до 1")
        
        breakTimer.stopBreak()
        
        print("✅ Тест статистики отдыха пройден")
    }
    
    static func testBreakActivityDetection() {
        print("🧪 Тест обнаружения активности во время отдыха...")
        
        let breakTimer = BreakTimer()
        var activityDetected = false
        
        // Настраиваем колбэк для обнаружения активности
        breakTimer.onActivityDetected = {
            activityDetected = true
        }
        
        // Запускаем отдых
        breakTimer.startBreak(duration: 5)
        
        // Симулируем активность (в реальном приложении это будет обнаружено автоматически)
        // Здесь мы просто проверяем, что колбэк настроен правильно
        assert(breakTimer.onActivityDetected != nil, "Колбэк обнаружения активности должен быть настроен")
        
        breakTimer.stopBreak()
        
        print("✅ Тест обнаружения активности во время отдыха пройден")
    }
}

#if DEBUG
extension BreakTimerTests {
    static func runQuickTest() {
        print("🧪 Быстрый тест основной функциональности...")
        
        // Создаем компоненты
        let pomodoroTimer = PomodoroTimer()
        let statsManager = StatisticsManager()
        
        // Тестируем базовую функциональность
        assert(pomodoroTimer.state == .idle)
        
        pomodoroTimer.startBreak()
        assert(pomodoroTimer.state == .onBreak)
        
        pomodoroTimer.stopBreak()
        assert(pomodoroTimer.state == .idle)
        
        // Тестируем запись статистики
        let initialBreaks = statsManager.getBreaksForToday()
        statsManager.recordCompletedBreak(duration: 900, wasComputerActive: false, computerActiveTime: 0)
        let newBreaks = statsManager.getBreaksForToday()
        assert(newBreaks > initialBreaks)
        
        print("✅ Быстрый тест пройден успешно!")
    }
}
#endif
