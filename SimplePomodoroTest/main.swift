import Cocoa

// Инициализируем систему логирования и обработку крашей
logInfo("Main", "🎯 uProd запускается")
CrashHandler.shared.setup()

// Создаем приложение
let app = NSApplication.shared

// Создаем и устанавливаем делегат
let delegate = AppDelegate()
app.delegate = delegate

// Тесты отключены - они создавали лишние записи в статистике
// #if DEBUG
// BreakTimerTests.runQuickTest()
// #endif

// Запускаем мониторинг
CrashHandler.shared.startPeriodicStateLogging()
CrashHandler.shared.startPerformanceMonitoring()

// Запускаем приложение
logInfo("Main", "🔄 Основной цикл запущен")
NSLog("🚀 uProd: Starting main application loop...")
app.run()
