import Cocoa

// MARK: - ProjectSelectionDelegate

protocol ProjectSelectionDelegate: AnyObject {
    func didUpdateProjectSelection(selectedProjects: Set<UUID>, selectedTypes: Set<ProjectType>, isAllSelected: Bool)
}

// MARK: - ProjectSelectionViewController

class ProjectSelectionViewController: NSViewController {
    
    // MARK: - Properties
    
    weak var delegate: ProjectSelectionDelegate?
    private let projectManager: ProjectManager
    private var selectedProjectIds: Set<UUID>
    private var selectedProjectTypes: Set<ProjectType>
    private var isAllSelected: Bool
    
    // UI Elements
    private var scrollView: NSScrollView!
    private var contentView: NSView!
    private var allProjectsCheckbox: NSButton!
    private var projectCheckboxes: [UUID: NSButton] = [:]
    private var typeCheckboxes: [ProjectType: NSButton] = [:]
    private var applyButton: NSButton!
    
    // MARK: - Initialization
    
    init(projectManager: ProjectManager, selectedProjectIds: Set<UUID>, selectedProjectTypes: Set<ProjectType>, isAllSelected: Bool) {
        self.projectManager = projectManager
        self.selectedProjectIds = selectedProjectIds
        self.selectedProjectTypes = selectedProjectTypes
        self.isAllSelected = isAllSelected
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - View Lifecycle
    
    override func loadView() {
        view = NSView(frame: NSRect(x: 0, y: 0, width: 450, height: 500))
        print("🔧 DEBUG: Создаем ProjectSelectionViewController с размером: 450x500")
        view.wantsLayer = true
        view.layer?.backgroundColor = NSColor.controlBackgroundColor.cgColor
        view.layer?.cornerRadius = 12

        // Устанавливаем минимальный размер
        view.setFrameSize(NSSize(width: 450, height: 500))
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        updateCheckboxStates()
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        print("🔧 DEBUG: Настраиваем UI для ProjectSelectionViewController")

        // Устанавливаем фон для view
        view.wantsLayer = true
        view.layer?.backgroundColor = NSColor.controlBackgroundColor.cgColor

        // Заголовок
        let titleLabel = NSTextField(labelWithString: "Фильтр проектов")
        titleLabel.font = NSFont.systemFont(ofSize: 16, weight: .bold)
        titleLabel.textColor = NSColor.labelColor

        // Чекбокс "Все проекты"
        allProjectsCheckbox = NSButton(checkboxWithTitle: "Все проекты", target: self, action: #selector(allProjectsToggled))
        allProjectsCheckbox.font = NSFont.systemFont(ofSize: 14, weight: .semibold)
        allProjectsCheckbox.state = isAllSelected ? .on : .off

        // Секция "По типам проектов"
        let typesHeaderLabel = NSTextField(labelWithString: "По типам:")
        typesHeaderLabel.font = NSFont.systemFont(ofSize: 14, weight: .semibold)
        typesHeaderLabel.textColor = NSColor.labelColor

        // Чекбоксы для типов проектов
        var typeCheckboxes: [NSButton] = []
        for projectType in ProjectType.allCases {
            let checkbox = NSButton(checkboxWithTitle: "\(projectType.emoji) \(projectType.displayName)", target: self, action: #selector(projectTypeCheckboxToggled(_:)))
            checkbox.font = NSFont.systemFont(ofSize: 13)
            checkbox.identifier = NSUserInterfaceItemIdentifier(projectType.rawValue)
            checkbox.state = selectedProjectTypes.contains(projectType) ? .on : .off
            typeCheckboxes.append(checkbox)
        }

        // Секция "Конкретные проекты"
        let projectsHeaderLabel = NSTextField(labelWithString: "Проекты:")
        projectsHeaderLabel.font = NSFont.systemFont(ofSize: 14, weight: .semibold)
        projectsHeaderLabel.textColor = NSColor.labelColor

        // Чекбоксы для конкретных проектов
        var projectCheckboxes: [NSButton] = []
        let activeProjects = projectManager.getActiveProjects()
        for project in activeProjects.prefix(5) { // Показываем первые 5 проектов
            let checkbox = NSButton(checkboxWithTitle: project.displayName, target: self, action: #selector(projectCheckboxToggled(_:)))
            checkbox.font = NSFont.systemFont(ofSize: 13)
            checkbox.identifier = NSUserInterfaceItemIdentifier(project.id.uuidString)
            checkbox.state = selectedProjectIds.contains(project.id) ? .on : .off
            projectCheckboxes.append(checkbox)
        }

        // Кнопка применить
        applyButton = NSButton(title: "Применить", target: self, action: #selector(applyButtonClicked))
        applyButton.bezelStyle = .rounded
        applyButton.font = NSFont.systemFont(ofSize: 14, weight: .medium)
        applyButton.contentTintColor = NSColor.white
        applyButton.wantsLayer = true
        applyButton.layer?.backgroundColor = NSColor.systemBlue.cgColor
        applyButton.layer?.cornerRadius = 8
        applyButton.isBordered = false

        // Добавляем все элементы
        view.addSubview(titleLabel)
        view.addSubview(allProjectsCheckbox)
        view.addSubview(typesHeaderLabel)
        for checkbox in typeCheckboxes {
            view.addSubview(checkbox)
        }
        view.addSubview(projectsHeaderLabel)
        for checkbox in projectCheckboxes {
            view.addSubview(checkbox)
        }
        view.addSubview(applyButton)

        // Отключаем автоматические constraints
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        allProjectsCheckbox.translatesAutoresizingMaskIntoConstraints = false
        typesHeaderLabel.translatesAutoresizingMaskIntoConstraints = false
        for checkbox in typeCheckboxes {
            checkbox.translatesAutoresizingMaskIntoConstraints = false
        }
        projectsHeaderLabel.translatesAutoresizingMaskIntoConstraints = false
        for checkbox in projectCheckboxes {
            checkbox.translatesAutoresizingMaskIntoConstraints = false
        }
        applyButton.translatesAutoresizingMaskIntoConstraints = false

        var constraints: [NSLayoutConstraint] = [
            // Заголовок
            titleLabel.topAnchor.constraint(equalTo: view.topAnchor, constant: 20),
            titleLabel.centerXAnchor.constraint(equalTo: view.centerXAnchor),

            // Чекбокс "Все проекты"
            allProjectsCheckbox.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 20),
            allProjectsCheckbox.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            allProjectsCheckbox.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),

            // Заголовок типов
            typesHeaderLabel.topAnchor.constraint(equalTo: allProjectsCheckbox.bottomAnchor, constant: 20),
            typesHeaderLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            typesHeaderLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
        ]

        // Добавляем constraints для чекбоксов типов
        var lastView: NSView = typesHeaderLabel
        for checkbox in typeCheckboxes {
            constraints.append(contentsOf: [
                checkbox.topAnchor.constraint(equalTo: lastView.bottomAnchor, constant: 8),
                checkbox.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 30),
                checkbox.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            ])
            lastView = checkbox
        }

        // Заголовок проектов
        constraints.append(contentsOf: [
            projectsHeaderLabel.topAnchor.constraint(equalTo: lastView.bottomAnchor, constant: 15),
            projectsHeaderLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            projectsHeaderLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
        ])
        lastView = projectsHeaderLabel

        // Добавляем constraints для чекбоксов проектов
        for checkbox in projectCheckboxes {
            constraints.append(contentsOf: [
                checkbox.topAnchor.constraint(equalTo: lastView.bottomAnchor, constant: 8),
                checkbox.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 30),
                checkbox.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            ])
            lastView = checkbox
        }

        // Кнопка применить
        constraints.append(contentsOf: [
            applyButton.topAnchor.constraint(equalTo: lastView.bottomAnchor, constant: 20),
            applyButton.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            applyButton.widthAnchor.constraint(equalToConstant: 120),
            applyButton.heightAnchor.constraint(equalToConstant: 36),
            applyButton.bottomAnchor.constraint(lessThanOrEqualTo: view.bottomAnchor, constant: -20)
        ])

        NSLayoutConstraint.activate(constraints)

        print("🔧 DEBUG: UI настроен, добавлены элементы")
    }

    


    // MARK: - Actions

    @objc private func allProjectsToggled() {
        isAllSelected = allProjectsCheckbox.state == .on
        updateCheckboxStates()
    }

    @objc private func projectTypeCheckboxToggled(_ sender: NSButton) {
        guard let identifier = sender.identifier else { return }

        // Находим тип проекта по identifier
        for projectType in ProjectType.allCases {
            if projectType.rawValue == identifier.rawValue {
                if sender.state == .on {
                    selectedProjectTypes.insert(projectType)
                    // Автоматически снимаем "Все проекты" при выборе конкретного типа
                    isAllSelected = false
                } else {
                    selectedProjectTypes.remove(projectType)
                }
                break
            }
        }

        updateCheckboxStates()
    }



    @objc private func projectCheckboxToggled(_ sender: NSButton) {
        guard let identifier = sender.identifier,
              let projectId = UUID(uuidString: identifier.rawValue) else { return }

        if sender.state == .on {
            selectedProjectIds.insert(projectId)
            // Автоматически снимаем "Все проекты" при выборе конкретного проекта
            isAllSelected = false
        } else {
            selectedProjectIds.remove(projectId)
        }

        updateCheckboxStates()
    }

    @objc private func applyButtonClicked() {
        delegate?.didUpdateProjectSelection(
            selectedProjects: selectedProjectIds,
            selectedTypes: selectedProjectTypes,
            isAllSelected: isAllSelected
        )
    }

    // MARK: - Helper Methods

    private func updateCheckboxStates() {
        // Если ничего не выбрано, автоматически включаем "Все проекты"
        if selectedProjectIds.isEmpty && selectedProjectTypes.isEmpty && !isAllSelected {
            isAllSelected = true
        }

        // Обновляем состояние чекбокса "Все проекты"
        allProjectsCheckbox.state = isAllSelected ? .on : .off

        // Если выбрано "Все проекты", очищаем остальные выборы
        if isAllSelected {
            selectedProjectIds.removeAll()
            selectedProjectTypes.removeAll()
        }

        print("🔧 DEBUG: Обновляем состояния чекбоксов. isAllSelected: \(isAllSelected), selectedProjects: \(selectedProjectIds.count), selectedTypes: \(selectedProjectTypes.count)")
    }
}
