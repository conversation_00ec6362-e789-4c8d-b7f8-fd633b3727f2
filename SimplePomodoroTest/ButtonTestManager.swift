import Cocoa
import Foundation

class ButtonTestManager {
    static let shared = ButtonTestManager()
    
    private init() {}
    
    func runButtonTests() {
        print("🧪 ButtonTestManager: Начинаем тестирование кнопок...")
        NSLog("🧪 ButtonTestManager: Начинаем тестирование кнопок...")

        // Простой тест: создаем диалог и сразу тестируем кнопки
        testSimpleButtonClicks()
    }

    private func testSimpleButtonClicks() {
        print("🧪 Простой тест кнопок: Создание диалога")
        NSLog("🧪 Простой тест кнопок: Создание диалога")

        let projectManager = ProjectManager()
        let dialog = ProjectEditDialog(projectManager: projectManager) { project in
            print("🧪 COMPLETION ВЫЗВАН! Проект: \(project?.name ?? "nil")")
            NSLog("🧪 COMPLETION ВЫЗВАН! Проект: \(project?.name ?? "nil")")
        }

        // Показываем диалог
        dialog.showDialog()

        // Ждем немного и тестируем кнопки
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.testSpecificButtons(dialog: dialog)
        }
    }
    
    private func testProjectManagementWindow() {
        print("🧪 Тест 1: Создание ProjectManagementWindow")
        NSLog("🧪 Тест 1: Создание ProjectManagementWindow")
        
        let projectManager = ProjectManager()
        let window = ProjectManagementWindow(projectManager: projectManager)
        
        // Проверяем, что окно создалось
        if window.window != nil {
            print("✅ ProjectManagementWindow создано успешно")
            NSLog("✅ ProjectManagementWindow создано успешно")
        } else {
            print("❌ ProjectManagementWindow не создано")
            NSLog("❌ ProjectManagementWindow не создано")
        }
        
        // Показываем окно
        window.showWindow()
        
        // Проверяем кнопку создания проекта через рефлексию
        if let createButton = self.getCreateButton(from: window) {
            print("✅ Кнопка 'Создать' найдена")
            NSLog("✅ Кнопка 'Создать' найдена")
            print("🔍 Target: \(String(describing: createButton.target))")
            print("🔍 Action: \(String(describing: createButton.action))")
            NSLog("🔍 Target: \(String(describing: createButton.target))")
            NSLog("🔍 Action: \(String(describing: createButton.action))")
            
            // Симулируем нажатие кнопки
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                print("🧪 Симулируем нажатие кнопки 'Создать'")
                NSLog("🧪 Симулируем нажатие кнопки 'Создать'")
                createButton.performClick(nil)
            }
        } else {
            print("❌ Кнопка 'Создать' не найдена")
            NSLog("❌ Кнопка 'Создать' не найдена")
        }
    }
    
    private func testProjectEditDialog() {
        print("🧪 Тест 2: Создание ProjectEditDialog")
        NSLog("🧪 Тест 2: Создание ProjectEditDialog")
        
        let projectManager = ProjectManager()
        let dialog = ProjectEditDialog(projectManager: projectManager) { project in
            print("🧪 Completion вызван с проектом: \(project?.name ?? "nil")")
            NSLog("🧪 Completion вызван с проектом: \(project?.name ?? "nil")")
        }
        
        // Проверяем, что диалог создался
        if dialog.window != nil {
            print("✅ ProjectEditDialog создан успешно")
            NSLog("✅ ProjectEditDialog создан успешно")
        } else {
            print("❌ ProjectEditDialog не создан")
            NSLog("❌ ProjectEditDialog не создан")
            return
        }
        
        // Показываем диалог
        dialog.showDialog()
        
        // Проверяем кнопки через рефлексию
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.testDialogButtons(dialog: dialog)
        }
    }
    
    private func testDialogButtons(dialog: ProjectEditDialog) {
        print("🧪 Тест 3: Проверка кнопок в диалоге")
        NSLog("🧪 Тест 3: Проверка кнопок в диалоге")
        
        guard let window = dialog.window,
              let contentView = window.contentView else {
            print("❌ Не удалось получить contentView")
            NSLog("❌ Не удалось получить contentView")
            return
        }
        
        // Ищем все кнопки в диалоге
        let buttons = findAllButtons(in: contentView)
        print("🔍 Найдено кнопок: \(buttons.count)")
        NSLog("🔍 Найдено кнопок: \(buttons.count)")
        
        for (index, button) in buttons.enumerated() {
            print("🔍 Кнопка \(index + 1): '\(button.title)'")
            print("   Target: \(String(describing: button.target))")
            print("   Action: \(String(describing: button.action))")
            print("   Enabled: \(button.isEnabled)")
            NSLog("🔍 Кнопка \(index + 1): '\(button.title)', Target: \(String(describing: button.target)), Action: \(String(describing: button.action)), Enabled: \(button.isEnabled)")
        }
        
        // Тестируем кнопки
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.simulateButtonClicks(buttons: buttons)
        }
    }
    
    private func simulateButtonClicks(buttons: [NSButton]) {
        print("🧪 Тест 4: Симуляция нажатий кнопок")
        NSLog("🧪 Тест 4: Симуляция нажатий кнопок")

        for (index, button) in buttons.enumerated() {
            DispatchQueue.main.asyncAfter(deadline: .now() + Double(index) * 0.5) {
                print("🧪 Нажимаем кнопку '\(button.title)'")
                NSLog("🧪 Нажимаем кнопку '\(button.title)'")

                // Всегда пытаемся нажать кнопку, независимо от target/action
                // так как в логах видно, что они есть
                button.performClick(nil)
                print("✅ Кнопка '\(button.title)' нажата (performClick выполнен)")
                NSLog("✅ Кнопка '\(button.title)' нажата (performClick выполнен)")
            }
        }
    }
    
    private func testSpecificButtons(dialog: ProjectEditDialog) {
        print("🧪 Тестируем конкретные кнопки")
        NSLog("🧪 Тестируем конкретные кнопки")

        // Получаем кнопки через рефлексию
        let mirror = Mirror(reflecting: dialog)
        var saveButton: NSButton?
        var cancelButton: NSButton?

        for child in mirror.children {
            if child.label == "saveButton" {
                saveButton = child.value as? NSButton
            } else if child.label == "cancelButton" {
                cancelButton = child.value as? NSButton
            }
        }

        // Тестируем кнопку сохранения
        if let save = saveButton {
            print("🧪 Найдена кнопка сохранения: '\(save.title)'")
            print("🧪 Target: \(String(describing: save.target)), Action: \(String(describing: save.action))")
            NSLog("🧪 Найдена кнопка сохранения: '\(save.title)', Target: \(String(describing: save.target)), Action: \(String(describing: save.action))")

            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                print("🧪 НАЖИМАЕМ КНОПКУ СОХРАНЕНИЯ!")
                NSLog("🧪 НАЖИМАЕМ КНОПКУ СОХРАНЕНИЯ!")
                save.performClick(nil)
                print("🧪 performClick выполнен для кнопки сохранения")
                NSLog("🧪 performClick выполнен для кнопки сохранения")
            }
        } else {
            print("❌ Кнопка сохранения не найдена")
            NSLog("❌ Кнопка сохранения не найдена")
        }

        // Тестируем кнопку отмены через 2 секунды
        if let cancel = cancelButton {
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                print("🧪 НАЖИМАЕМ КНОПКУ ОТМЕНЫ!")
                NSLog("🧪 НАЖИМАЕМ КНОПКУ ОТМЕНЫ!")
                cancel.performClick(nil)
                print("🧪 performClick выполнен для кнопки отмены")
                NSLog("🧪 performClick выполнен для кнопки отмены")
            }
        }
    }

    // MARK: - Helper Methods

    private func getCreateButton(from window: ProjectManagementWindow) -> NSButton? {
        // Используем рефлексию для получения приватной кнопки
        let mirror = Mirror(reflecting: window)
        for child in mirror.children {
            if child.label == "createButton" {
                return child.value as? NSButton
            }
        }
        return nil
    }
    
    private func findAllButtons(in view: NSView) -> [NSButton] {
        var buttons: [NSButton] = []
        
        if let button = view as? NSButton {
            buttons.append(button)
        }
        
        for subview in view.subviews {
            buttons.append(contentsOf: findAllButtons(in: subview))
        }
        
        return buttons
    }
}
