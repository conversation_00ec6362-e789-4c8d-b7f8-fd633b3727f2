import Foundation

/// Простой тест для проверки работы звуковой системы
class SoundTest {
    
    static func runTest() {
        print("🔊 SoundTest: Начинаем тест звуковой системы...")
        
        // Тест SoundSettings
        let settings = SoundSettings.shared
        print("🔊 SoundTest: Настройки звуков:")
        print(settings.debugDescription())
        
        // Тест SoundManager
        let soundManager = SoundManager()
        print("🔊 SoundTest: Доступные звуки:")
        for sound in SoundManager.availableSounds {
            print("  - \(sound.displayName) (\(sound.fileName))")
        }
        
        // Тест воспроизведения (без реального звука)
        print("🔊 SoundTest: Тестируем воспроизведение звуков...")
        soundManager.playSound(for: .sessionCompleted)
        soundManager.playSound(for: .breakCompleted)
        
        print("🔊 SoundTest: Тест завершен!")
    }
}
