import Cocoa

/// МАКСИМАЛЬНО ПРОСТАЯ КНОПКА НА ОСНОВЕ NSButton
/// НИКАКИХ КАСТОМНЫХ VIEW, НИКАКИХ MOUSE EVENTS
class SuperSimpleButton: NSButton {
    
    init(title: String, isGreen: Bool = false) {
        super.init(frame: .zero)
        
        // КРИТИЧЕСКИ ВАЖНО
        self.translatesAutoresizingMaskIntoConstraints = false
        
        // Базовые настройки
        self.title = title
        self.bezelStyle = .rounded
        self.controlSize = .large
        self.font = NSFont.systemFont(ofSize: 14, weight: .medium)
        
        // Цвет
        if isGreen {
            self.contentTintColor = NSColor.systemGreen
        } else {
            self.contentTintColor = NSColor.systemGray
        }
        
        // НИКАКИХ КАСТОМНЫХ МЕТОДОВ
        // НИКАКИХ ПЕРЕОПРЕДЕЛЕНИЙ
        // НИЧЕГО ЛИШНЕГО
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}
