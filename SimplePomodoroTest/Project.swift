import Foundation

// MARK: - Project Type Enum

enum ProjectType: String, CaseIterable, Codable {
    case work = "Рабочий"
    case personal = "Личный"
    case learning = "Обучение"
    case health = "Здоровье"
    case creative = "Творчество"
    
    var displayName: String {
        return self.rawValue
    }
    
    var emoji: String {
        switch self {
        case .work:
            return "💼"
        case .personal:
            return "🏠"
        case .learning:
            return "📚"
        case .health:
            return "💪"
        case .creative:
            return "🎨"
        }
    }
}

// MARK: - Project Model

struct Project: Codable, Identifiable, Equatable {
    let id: UUID
    var name: String
    var type: ProjectType
    var parentId: UUID?          // для иерархии проектов
    var color: String?           // hex цвет для визуального различения
    var isActive: Bool           // активен ли проект
    var isArchived: Bool         // архивирован ли проект
    var isWorkRelated: Bool      // рабочий проект (true) или личный (false) - оставлено для совместимости
    var customEmoji: String?     // кастомный эмодзи для проекта
    let createdAt: Date
    var lastUsedAt: Date?        // для определения популярности

    init(name: String, type: ProjectType, parentId: UUID? = nil, color: String? = nil, customEmoji: String? = nil, isWorkRelated: Bool? = nil) {
        self.id = UUID()
        self.name = name
        self.type = type
        self.parentId = parentId
        self.color = color
        self.customEmoji = customEmoji
        self.isActive = true
        self.isArchived = false
        self.createdAt = Date()
        self.lastUsedAt = nil

        // Автоматически определяем рабочий/личный на основе типа, если не указано явно (для совместимости)
        if let isWorkRelated = isWorkRelated {
            self.isWorkRelated = isWorkRelated
        } else {
            self.isWorkRelated = type == .work
        }
    }
    
    // MARK: - Helper Methods
    
    /// Обновляет время последнего использования
    mutating func markAsUsed() {
        self.lastUsedAt = Date()
    }
    
    /// Архивирует проект
    mutating func archive() {
        self.isArchived = true
        self.isActive = false
    }
    
    /// Разархивирует проект
    mutating func unarchive() {
        self.isArchived = false
        self.isActive = true
    }
    
    /// Возвращает эмодзи проекта (кастомный или типа)
    var effectiveEmoji: String {
        return customEmoji ?? type.emoji
    }

    /// Возвращает отображаемое имя с эмодзи
    var displayName: String {
        return "\(effectiveEmoji) \(name)"
    }

    /// Возвращает полное отображаемое имя с типом
    var fullDisplayName: String {
        return "\(effectiveEmoji) \(name) (\(type.displayName))"
    }

    /// Возвращает цвет проекта или цвет по умолчанию
    var effectiveColor: String {
        return color ?? defaultColorForType
    }

    /// Возвращает цвет по умолчанию для типа проекта
    private var defaultColorForType: String {
        switch type {
        case .work:
            return "#3B82F6"  // Синий
        case .personal:
            return "#10B981"  // Зеленый
        case .learning:
            return "#8B5CF6"  // Фиолетовый
        case .health:
            return "#EF4444"  // Красный
        case .creative:
            return "#F59E0B"  // Оранжевый
        }
    }
    
    // MARK: - Equatable
    
    static func == (lhs: Project, rhs: Project) -> Bool {
        return lhs.id == rhs.id
    }
}

// MARK: - Default Projects

extension Project {
    /// Создает проекты по умолчанию для новых пользователей
    static func createDefaultProjects() -> [Project] {
        return [
            Project(name: "Работа", type: .work, color: "#3B82F6", isWorkRelated: true),
            Project(name: "Личные дела", type: .personal, color: "#10B981", isWorkRelated: false),
            Project(name: "Обучение", type: .learning, color: "#8B5CF6", isWorkRelated: false)
        ]
    }
}
