import Foundation
import Cocoa

/// Утилита для работы с логами
class LogViewer {
    static let shared = LogViewer()

    private let fileManager = FileManager.default

    private init() {}
    
    /// Получить путь к директории с логами
    private func getLogsDirectory() -> URL? {
        // В sandbox режиме логи создаются в контейнере приложения
        let appSupport = fileManager.urls(for: .applicationSupportDirectory, in: .userDomainMask).first!
        let appSupportLogs = appSupport.appendingPathComponent("uProd/Logs")

        if fileManager.fileExists(atPath: appSupportLogs.path) {
            return appSupportLogs
        }

        // Fallback к старому пути в Documents
        let documents = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        let documentsLogs = documents.appendingPathComponent("uProd_Logs")
        if fileManager.fileExists(atPath: documentsLogs.path) {
            return documentsLogs
        }

        // Fallback к временной директории
        let tempLogs = URL(fileURLWithPath: NSTemporaryDirectory()).appendingPathComponent("uProd_Logs")
        if fileManager.fileExists(atPath: tempLogs.path) {
            return tempLogs
        }

        return appSupportLogs // Возвращаем Application Support как основной путь
    }
    
    /// Открыть директорию с логами в Finder
    func openLogsInFinder() {
        guard let logsDir = getLogsDirectory() else {
            logError("LogViewer", "❌ Не удалось найти директорию с логами")
            showAlert(title: "Ошибка", message: "Не удалось найти директорию с логами")
            return
        }

        logInfo("LogViewer", "📋 Открываем папку логов: \(logsDir.path)")
        NSWorkspace.shared.open(logsDir)
        logInfo("LogViewer", "✅ Папка логов открыта в Finder")
    }


    // MARK: - Private Methods

    private func showAlert(title: String, message: String) {
        let alert = NSAlert()
        alert.messageText = title
        alert.informativeText = message
        alert.addButton(withTitle: "OK")
        alert.runModal()
    }
}
