import Foundation

class DemoDataManager {
    private let statisticsManager: StatisticsManager
    
    init(statisticsManager: StatisticsManager) {
        self.statisticsManager = statisticsManager
    }
    
    // MARK: - Создание демо данных
    
    func createDemoData() {
        print("📊 DemoDataManager: Начинаем создание демо данных...")

        // Очищаем существующие данные
        statisticsManager.clearAllIntervals()

        let calendar = Calendar.current
        let now = Date()

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "dd.MM.yyyy HH:mm"
        print("📊 Текущее время: \(dateFormatter.string(from: now))")

        // Создаем демо данные для трех недель
        createCurrentWeekData(calendar: calendar, now: now)
        createPreviousWeekData(calendar: calendar, now: now)
        createTwoWeeksAgoData(calendar: calendar, now: now)

        print("📊 DemoDataManager: Созданы демо данные с различными проблемами по неделям")
    }
    
    // MARK: - Текущая неделя: Проблема с поздним началом работы
    
    private func createCurrentWeekData(calendar: Calendar, now: Date) {
        // Получаем начало текущей недели (понедельник)
        let weekday = calendar.component(.weekday, from: now)
        let daysFromMonday = (weekday == 1) ? 6 : weekday - 2
        let startOfWeek = calendar.date(byAdding: .day, value: -daysFromMonday, to: calendar.startOfDay(for: now))!
        
        // Создаем данные для каждого дня недели
        for dayOffset in 0..<7 {
            guard let workDay = calendar.date(byAdding: .day, value: dayOffset, to: startOfWeek) else { continue }
            
            // Пропускаем выходные
            let dayOfWeek = calendar.component(.weekday, from: workDay)
            if dayOfWeek == 1 || dayOfWeek == 7 { continue } // Воскресенье или суббота
            
            // Пропускаем будущие дни
            if workDay > now { continue }
            
            // ПРОБЛЕМА: Начинаем работу поздно (после 12:00)
            let lateStartHour = Int.random(in: 12...14) // Начинаем между 12:00 и 14:00
            let startMinute = Int.random(in: 0...59)
            
            // Создаем 4-6 интервалов с нормальной продолжительностью
            let intervalsCount = Int.random(in: 4...6)
            
            for intervalIndex in 0..<intervalsCount {
                let intervalHour = lateStartHour + (intervalIndex * 1) // Каждый час
                let intervalMinute = startMinute + Int.random(in: 0...30)
                
                if let intervalTime = calendar.date(bySettingHour: intervalHour, minute: intervalMinute, second: 0, of: workDay) {
                    // Нормальная продолжительность 52 минуты (3120 секунд)
                    let duration: TimeInterval = 3120 + TimeInterval(Int.random(in: -300...300)) // ±5 минут
                    addInterval(date: intervalTime, duration: duration)
                }
            }
        }
    }
    
    // MARK: - Прошлая неделя: Проблема с переработкой
    
    private func createPreviousWeekData(calendar: Calendar, now: Date) {
        // Получаем начало прошлой недели
        let weekday = calendar.component(.weekday, from: now)
        let daysFromMonday = (weekday == 1) ? 6 : weekday - 2
        let startOfCurrentWeek = calendar.date(byAdding: .day, value: -daysFromMonday, to: calendar.startOfDay(for: now))!
        let startOfPreviousWeek = calendar.date(byAdding: .day, value: -7, to: startOfCurrentWeek)!
        
        // Создаем данные для каждого дня недели
        for dayOffset in 0..<7 {
            guard let workDay = calendar.date(byAdding: .day, value: dayOffset, to: startOfPreviousWeek) else { continue }
            
            // Пропускаем выходные
            let dayOfWeek = calendar.component(.weekday, from: workDay)
            if dayOfWeek == 1 || dayOfWeek == 7 { continue }
            
            // Нормальное начало работы в 8-9 утра (раннее начало)
            let startHour = Int.random(in: 8...9)
            let startMinute = Int.random(in: 0...30)

            // Создаем 4-5 интервалов (нормальное количество)
            let intervalsCount = Int.random(in: 4...5)

            for intervalIndex in 0..<intervalsCount {
                // Интервалы идут подряд с небольшими перерывами
                let intervalHour = startHour + intervalIndex
                let intervalMinute = startMinute + Int.random(in: 0...10)

                // Убеждаемся, что не выходим за рабочий день
                if intervalHour < 18, let intervalTime = calendar.date(bySettingHour: intervalHour, minute: intervalMinute, second: 0, of: workDay) {
                    // ПРОБЛЕМА: Переработка - интервалы длятся 100-120 минут вместо 52
                    let overtimeDuration: TimeInterval = TimeInterval(Int.random(in: 6000...7200)) // 100-120 минут
                    addInterval(date: intervalTime, duration: overtimeDuration)
                }
            }
        }
    }
    
    // MARK: - Две недели назад: Проблема с отсутствием работы
    
    private func createTwoWeeksAgoData(calendar: Calendar, now: Date) {
        print("🔧 DemoDataManager: Создаем данные для 2 недель назад...")

        // Получаем начало недели две недели назад
        let weekday = calendar.component(.weekday, from: now)
        let daysFromMonday = (weekday == 1) ? 6 : weekday - 2
        let startOfCurrentWeek = calendar.date(byAdding: .day, value: -daysFromMonday, to: calendar.startOfDay(for: now))!
        let startOfTwoWeeksAgo = calendar.date(byAdding: .day, value: -14, to: startOfCurrentWeek)!

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "dd.MM.yyyy"
        print("🔧 Период 2 недели назад: \(dateFormatter.string(from: startOfTwoWeeksAgo)) - \(dateFormatter.string(from: calendar.date(byAdding: .day, value: 7, to: startOfTwoWeeksAgo)!))")
        
        // ПРОБЛЕМА: Работал только один день из всей недели
        let workingDayOffset = Int.random(in: 1...5) // Случайный рабочий день (пн-пт)
        
        for dayOffset in 0..<7 {
            guard let workDay = calendar.date(byAdding: .day, value: dayOffset, to: startOfTwoWeeksAgo) else { continue }
            
            // Пропускаем выходные
            let dayOfWeek = calendar.component(.weekday, from: workDay)
            if dayOfWeek == 1 || dayOfWeek == 7 { continue }
            
            // Работаем только в один выбранный день
            if dayOffset != workingDayOffset { continue }
            
            // В этот единственный рабочий день работаем нормально
            let startHour = Int.random(in: 9...10)
            let startMinute = Int.random(in: 0...30)
            
            // Создаем 6-8 интервалов (компенсируем отсутствие других дней)
            let intervalsCount = Int.random(in: 6...8)
            
            for intervalIndex in 0..<intervalsCount {
                let intervalHour = startHour + (intervalIndex * 1)
                let intervalMinute = startMinute + Int.random(in: 0...30)
                
                if let intervalTime = calendar.date(bySettingHour: intervalHour, minute: intervalMinute, second: 0, of: workDay) {
                    // Нормальная продолжительность
                    let duration: TimeInterval = 3120 + TimeInterval(Int.random(in: -300...300)) // ±5 минут
                    addInterval(date: intervalTime, duration: duration)

                    let dateFormatter = DateFormatter()
                    dateFormatter.dateFormat = "dd.MM HH:mm"
                    print("🔧 Добавлен интервал: \(dateFormatter.string(from: intervalTime)), длительность: \(Int(duration/60)) мин")
                }
            }
        }

        print("🔧 DemoDataManager: Завершено создание данных для 2 недель назад")
    }
    
    // MARK: - Вспомогательные методы
    
    private func addInterval(date: Date, duration: TimeInterval) {
        let interval = CompletedInterval(date: date, duration: duration)
        var intervals = statisticsManager.getAllStoredIntervals()
        intervals.append(interval)
        statisticsManager.saveIntervals(intervals)
    }
    
    // MARK: - Описание демо данных
    
    func getDemoDataDescription() -> String {
        return """
        📊 Демо данные содержат три недели с разными проблемами:
        
        🔴 Текущая неделя: Поздний старт
        • Начало работы после 12:00 (вместо 9:00)
        • Нормальная продолжительность интервалов (~52 мин)
        • Нормальное количество интервалов в день
        
        🟡 Прошлая неделя: Переработка
        • Нормальное начало работы (9-10 утра)
        • Длинные интервалы (100-120 мин вместо 52)
        • Увеличенное количество интервалов
        
        🟠 Две недели назад: Прогулы
        • Работа только в один день из недели
        • В рабочий день - нормальные интервалы
        • Компенсация количеством интервалов в единственный день
        """
    }
}
