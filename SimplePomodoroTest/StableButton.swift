import Cocoa

/// Стили кнопок
enum StableButtonStyle {
    case standard       // Стандартная кнопка macOS
    case primary        // Синяя акцентная кнопка
    case success        // Зеленая кнопка для успешных действий
    case secondary      // Серая кнопка
    case destructive    // Красная кнопка для опасных действий
}

/// Размеры кнопок
enum StableButtonSize {
    case small          // Маленькая кнопка (28px)
    case medium         // Средняя кнопка (36px) - по умолчанию
    case large          // Большая кнопка (44px)
}

/// Максимально простая и стабильная кнопка, которая НИКУДА НЕ УЕЗЖАЕТ
class StableButton: NSButton {

    private let buttonStyle: StableButtonStyle
    private let buttonSize: StableButtonSize
    private var gradientLayer: CAGradientLayer?

    init(title: String, style: StableButtonStyle = .standard, size: StableButtonSize = .medium) {
        self.buttonStyle = style
        self.buttonSize = size
        super.init(frame: .zero)

        // КРИТИЧЕСКИ ВАЖНО - отключаем autoresizing mask
        self.translatesAutoresizingMaskIntoConstraints = false

        self.title = title
        setupButton()
    }

    // Convenience init для обратной совместимости
    convenience init(title: String, isPrimary: Bool = false) {
        self.init(title: title, style: isPrimary ? .primary : .standard)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupButton() {
        // Отключаем стандартный стиль для кастомного градиента
        self.isBordered = false
        self.wantsLayer = true

        // Настройка размера и шрифта
        setupSizeAndFont()

        // Настройка градиента
        setupGradient()

        // Белый текст для всех кнопок с градиентом
        if buttonStyle != .standard {
            self.attributedTitle = NSAttributedString(
                string: self.title,
                attributes: [
                    .foregroundColor: NSColor.white,
                    .font: self.font ?? NSFont.systemFont(ofSize: 14, weight: .medium)
                ]
            )
        }

        // НИКАКИХ АНИМАЦИЙ, НИКАКИХ ТРАНСФОРМАЦИЙ
        // НИКАКИХ MOUSE EVENTS OVERRIDE
        // НИЧЕГО ЛИШНЕГО
    }

    private func setupSizeAndFont() {
        let (height, fontSize, fontWeight) = getSizeParameters()

        // Устанавливаем шрифт
        self.font = NSFont.systemFont(ofSize: fontSize, weight: fontWeight)

        // НЕ УСТАНАВЛИВАЕМ CONSTRAINTS ЗДЕСЬ - ЭТО ВЫЗЫВАЕТ ПРОБЛЕМЫ
        // Размер будет контролироваться родительским view
    }

    private func getSizeParameters() -> (height: CGFloat, fontSize: CGFloat, fontWeight: NSFont.Weight) {
        switch buttonSize {
        case .small:
            return (28, 12, .medium)
        case .medium:
            return (36, 14, .medium)
        case .large:
            return (44, 16, .semibold)
        }
    }

    private func setupGradient() {
        // Для стандартных кнопок используем системный стиль
        if buttonStyle == .standard {
            self.bezelStyle = .rounded
            self.isBordered = true
            return
        }

        // Создаем градиент для кастомных стилей
        gradientLayer = CAGradientLayer()

        switch buttonStyle {
        case .standard:
            break // Уже обработано выше

        case .primary:
            // Синий градиент
            let color1 = NSColor(red: 0.0, green: 0.48, blue: 1.0, alpha: 1.0)
            let color2 = NSColor(red: 0.0, green: 0.38, blue: 0.85, alpha: 1.0)
            gradientLayer?.colors = [color1.cgColor, color2.cgColor]

        case .success:
            // Зеленый градиент
            let color1 = NSColor(red: 0.2, green: 0.8, blue: 0.2, alpha: 1.0)
            let color2 = NSColor(red: 0.1, green: 0.6, blue: 0.1, alpha: 1.0)
            gradientLayer?.colors = [color1.cgColor, color2.cgColor]

        case .secondary:
            // Серый градиент
            let color1 = NSColor(red: 0.45, green: 0.45, blue: 0.5, alpha: 1.0)
            let color2 = NSColor(red: 0.35, green: 0.35, blue: 0.4, alpha: 1.0)
            gradientLayer?.colors = [color1.cgColor, color2.cgColor]

        case .destructive:
            // Красный градиент
            let color1 = NSColor(red: 1.0, green: 0.3, blue: 0.3, alpha: 1.0)
            let color2 = NSColor(red: 0.8, green: 0.1, blue: 0.1, alpha: 1.0)
            gradientLayer?.colors = [color1.cgColor, color2.cgColor]
        }

        // Настройка градиента
        if let gradientLayer = gradientLayer {
            gradientLayer.startPoint = CGPoint(x: 0, y: 0)
            gradientLayer.endPoint = CGPoint(x: 0, y: 1)
            gradientLayer.cornerRadius = 8

            // Тень
            gradientLayer.shadowColor = NSColor.black.cgColor
            gradientLayer.shadowOpacity = 0.3
            gradientLayer.shadowOffset = CGSize(width: 0, height: 2)
            gradientLayer.shadowRadius = 4

            self.layer = gradientLayer
        }
    }
    
    override func layout() {
        super.layout()
        // Обновляем размер градиента
        gradientLayer?.frame = bounds
    }

    // НЕ ПЕРЕОПРЕДЕЛЯЕМ mouseDown, mouseUp, mouseEntered, mouseExited
    // НЕ ДОБАВЛЯЕМ tracking areas
    // НЕ ДЕЛАЕМ НИЧЕГО, ЧТО МОЖЕТ ПОВЛИЯТЬ НА LAYOUT
}
