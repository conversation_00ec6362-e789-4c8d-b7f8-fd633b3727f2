import Cocoa

class IntervalCompletionWindow: NSWindow {

    private var reminderCount: Int = 0
    private var onComplete: (() -> Void)?
    private var onExtend: (() -> Void)?
    private var messageLabel: NSTextField!

    init() {
        // Создаем окно на весь экран
        let screenFrame = NSScreen.main?.frame ?? NSRect(x: 0, y: 0, width: 1920, height: 1080)

        super.init(
            contentRect: screenFrame,
            styleMask: [.borderless],
            backing: .buffered,
            defer: false
        )

        print("🔧 IntervalCompletionWindow: Инициализация окна")
        setupWindow()
        setupUI()
    }
    
    private func setupWindow() {
        print("🔧 IntervalCompletionWindow: Настройка окна")

        // Настройки окна
        self.level = .screenSaver  // Более высокий уровень
        self.isOpaque = false
        self.backgroundColor = NSColor.black.withAlphaComponent(0.85)
        self.ignoresMouseEvents = false
        self.hasShadow = false

        // Показываем на всех рабочих столах
        self.collectionBehavior = [.canJoinAllSpaces, .fullScreenAuxiliary, .stationary]

        // Принудительно делаем окно активным
        self.orderFrontRegardless()
    }

    // Переопределяем методы для возможности получения фокуса
    override var canBecomeKey: Bool {
        return true
    }

    override var canBecomeMain: Bool {
        return true
    }
    
    private func setupUI() {
        print("🔧 IntervalCompletionWindow: Настройка UI")

        let contentView = NSView(frame: self.contentRect(forFrameRect: self.frame))
        self.contentView = contentView

        // Основной контейнер
        let containerView = NSView()
        containerView.translatesAutoresizingMaskIntoConstraints = false
        containerView.wantsLayer = true
        containerView.layer?.backgroundColor = NSColor.windowBackgroundColor.cgColor
        containerView.layer?.cornerRadius = 25
        containerView.layer?.shadowColor = NSColor.black.cgColor
        containerView.layer?.shadowOpacity = 0.3
        containerView.layer?.shadowOffset = CGSize(width: 0, height: 10)
        containerView.layer?.shadowRadius = 20
        contentView.addSubview(containerView)
        
        // Заголовок
        let titleLabel = NSTextField(labelWithString: "🍅 Интервал завершен!")
        titleLabel.font = NSFont.systemFont(ofSize: 42, weight: .bold)
        titleLabel.textColor = NSColor.labelColor
        titleLabel.alignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(titleLabel)

        // Сообщение
        messageLabel = NSTextField(labelWithString: getMessageText())
        messageLabel.font = NSFont.systemFont(ofSize: 20)
        messageLabel.textColor = NSColor.secondaryLabelColor
        messageLabel.alignment = .center
        messageLabel.maximumNumberOfLines = 0
        messageLabel.preferredMaxLayoutWidth = 500
        messageLabel.translatesAutoresizingMaskIntoConstraints = false
        messageLabel.identifier = NSUserInterfaceItemIdentifier("messageLabel")
        containerView.addSubview(messageLabel)
        
        // Кнопки
        let buttonStackView = NSStackView()
        buttonStackView.orientation = .horizontal
        buttonStackView.spacing = 30
        buttonStackView.translatesAutoresizingMaskIntoConstraints = false

        let completeButton = SuperSimpleButton(title: "✅ Завершить интервал", isGreen: true)
        completeButton.target = self
        completeButton.action = #selector(completeButtonClicked)
        completeButton.keyEquivalent = "\r" // Enter

        let extendButton = SuperSimpleButton(title: "⏰ Поработать еще 5 минут", isGreen: false)
        extendButton.target = self
        extendButton.action = #selector(extendButtonClicked)

        buttonStackView.addArrangedSubview(extendButton)
        buttonStackView.addArrangedSubview(completeButton)
        containerView.addSubview(buttonStackView)
        
        // Constraints
        NSLayoutConstraint.activate([
            // Container
            containerView.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
            containerView.centerYAnchor.constraint(equalTo: contentView.centerYAnchor),
            containerView.widthAnchor.constraint(equalToConstant: 600),
            containerView.heightAnchor.constraint(equalToConstant: 400),

            // Title
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 50),
            titleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 30),
            titleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -30),

            // Message
            messageLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 40),
            messageLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 30),
            messageLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -30),

            // Buttons
            buttonStackView.topAnchor.constraint(equalTo: messageLabel.bottomAnchor, constant: 50),
            buttonStackView.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            buttonStackView.bottomAnchor.constraint(lessThanOrEqualTo: containerView.bottomAnchor, constant: -50)
        ])

        print("🔧 IntervalCompletionWindow: UI настроен")
    }
    
    private func getMessageText() -> String {
        switch reminderCount {
        case 0:
            return "Время для отдыха! Сделайте перерыв, чтобы сохранить продуктивность."
        case 1:
            return "Пожалуйста, сделайте перерыв. Переработка снижает эффективность."
        case 2:
            return "Серьезно, пора отдохнуть! Ваш мозг нуждается в восстановлении."
        case 3:
            return "⚠️ ВНИМАНИЕ! Продолжение работы без отдыха приведет к выгоранию!"
        case 4:
            return "🚨 КРИТИЧНО! Немедленно прекратите работу и отдохните!"
        default:
            return "🔥 ОПАСНОСТЬ ВЫГОРАНИЯ! Ваше здоровье важнее любых задач!"
        }
    }
    
    func updateMessage(reminderCount: Int) {
        print("🔧 IntervalCompletionWindow: Обновление сообщения, напоминание #\(reminderCount)")
        self.reminderCount = reminderCount

        messageLabel.stringValue = getMessageText()

        // Добавляем анимацию для привлечения внимания
        if reminderCount >= 2 {
            messageLabel.wantsLayer = true
            let animation = CABasicAnimation(keyPath: "transform.scale")
            animation.fromValue = 1.0
            animation.toValue = 1.05
            animation.duration = 0.5
            animation.autoreverses = true
            animation.repeatCount = 3
            messageLabel.layer?.add(animation, forKey: "pulse")
        }
    }
    
    func setCallbacks(onComplete: @escaping () -> Void, onExtend: @escaping () -> Void) {
        self.onComplete = onComplete
        self.onExtend = onExtend
    }
    
    @objc private func completeButtonClicked() {
        print("🔧 IntervalCompletionWindow: Нажата кнопка 'Завершить интервал'")

        // Добавляем небольшую задержку, чтобы кнопка успела завершить свой mouse event
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.hideWindow()
            self.onComplete?()
        }
    }

    @objc private func extendButtonClicked() {
        print("🔧 IntervalCompletionWindow: Нажата кнопка 'Поработать еще 5 минут'")

        // Добавляем небольшую задержку, чтобы кнопка успела завершить свой mouse event
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.hideWindow()
            self.onExtend?()
        }
    }

    private func hideWindow() {
        print("🔧 IntervalCompletionWindow: Скрытие окна")
        self.orderOut(nil)
        // Не используем close(), так как это может вызвать проблемы с циклом ссылок
    }
    
    override func keyDown(with event: NSEvent) {
        print("🔧 IntervalCompletionWindow: Нажата клавиша с кодом \(event.keyCode)")
        if event.keyCode == 53 { // Escape key
            completeButtonClicked()
        } else {
            super.keyDown(with: event)
        }
    }

    func showWindow() {
        print("🔧 IntervalCompletionWindow: Показ окна")
        self.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)

        // Принудительно делаем окно активным
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.makeKey()
            NSApp.activate(ignoringOtherApps: true)
        }
    }
}
