import Foundation

/// Трекер качества отдыха - отслеживает процент времени без активности
class BreakQualityTracker {
    
    // MARK: - Properties
    
    private let activityDetector = ActivityDetector()
    private var checkTimer: Timer?
    
    // Статистика текущего отдыха
    private var totalMinutes = 0
    private var activeMinutes = 0
    private var isTracking = false
    
    // Колбэк для завершения отдыха
    var onBreakCompleted: ((Int) -> Void)?
    
    // MARK: - Public Methods
    
    /// Начинает отслеживание качества отдыха
    func startTracking() {
        guard !isTracking else { return }
        
        print("📊 BreakQualityTracker: Начинаем отслеживание качества отдыха")
        isTracking = true
        totalMinutes = 0
        activeMinutes = 0
        
        // Запускаем детектор активности
        activityDetector.resetActivity()
        activityDetector.startMonitoring()
        
        // Запускаем таймер проверки каждую минуту
        checkTimer = Timer.scheduledTimer(withTimeInterval: 60.0, repeats: true) { [weak self] _ in
            self?.checkActivityForMinute()
        }
    }
    
    /// Останавливает отслеживание и возвращает процент качества отдыха
    func stopTracking() -> Int {
        guard isTracking else { return 100 }
        
        print("📊 BreakQualityTracker: Останавливаем отслеживание качества отдыха")
        isTracking = false
        
        // Останавливаем детектор активности
        activityDetector.stopMonitoring()
        
        // Останавливаем таймер
        checkTimer?.invalidate()
        checkTimer = nil
        
        // Рассчитываем качество отдыха
        let qualityPercentage = calculateQualityPercentage()
        
        print("📊 BreakQualityTracker: Качество отдыха: \(qualityPercentage)% (отдыхали \(totalMinutes - activeMinutes) из \(totalMinutes) минут)")
        
        // Вызываем колбэк
        onBreakCompleted?(qualityPercentage)
        
        return qualityPercentage
    }
    
    /// Принудительно останавливает отслеживание без расчета
    func forceStop() {
        guard isTracking else { return }
        
        print("📊 BreakQualityTracker: Принудительная остановка отслеживания")
        isTracking = false
        
        activityDetector.stopMonitoring()
        checkTimer?.invalidate()
        checkTimer = nil
    }
    
    /// Возвращает текущую статистику отдыха
    func getCurrentStats() -> (totalMinutes: Int, activeMinutes: Int, qualityPercentage: Int) {
        let quality = calculateQualityPercentage()
        return (totalMinutes, activeMinutes, quality)
    }
    
    // MARK: - Private Methods
    
    private func checkActivityForMinute() {
        totalMinutes += 1
        
        if activityDetector.wasActiveInLastMinute() {
            activeMinutes += 1
            print("📊 BreakQualityTracker: Минута \(totalMinutes) - АКТИВНОСТЬ обнаружена")
        } else {
            print("📊 BreakQualityTracker: Минута \(totalMinutes) - отдых")
        }
        
        let currentQuality = calculateQualityPercentage()
        print("📊 BreakQualityTracker: Текущее качество отдыха: \(currentQuality)%")
    }
    
    private func calculateQualityPercentage() -> Int {
        guard totalMinutes > 0 else { return 100 }
        
        let restMinutes = totalMinutes - activeMinutes
        let percentage = (restMinutes * 100) / totalMinutes
        
        return max(0, min(100, percentage))
    }
    
    deinit {
        forceStop()
    }
}
