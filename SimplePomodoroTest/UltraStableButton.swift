import Cocoa

/// МАКСИМАЛЬНО ПРОСТАЯ КНОПКА БЕЗ ВСЯКОГО ДЕРЬМА
/// НИКАКИХ CONSTRAINTS, НИКАКИХ АНИМАЦИЙ, НИЧЕГО
class UltraStableButton: NSView {
    
    private let titleLabel: NSTextField
    private let backgroundLayer: CAGradientLayer
    private var isPressed = false
    
    var target: AnyObject?
    var action: Selector?
    
    init(title: String, isGreen: Bool = false, isSmall: Bool = false) {
        // Создаем label для текста
        titleLabel = NSTextField(labelWithString: title)
        backgroundLayer = CAGradientLayer()
        
        super.init(frame: .zero)
        
        // КРИТИЧЕСКИ ВАЖНО
        self.translatesAutoresizingMaskIntoConstraints = false
        
        setupBackground(isGreen: isGreen)
        setupLabel(isSmall: isSmall)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupBackground(isGreen: Bool) {
        self.wantsLayer = true
        
        if isGreen {
            // Зеленый градиент
            let color1 = NSColor(red: 0.2, green: 0.8, blue: 0.2, alpha: 1.0)
            let color2 = NSColor(red: 0.1, green: 0.6, blue: 0.1, alpha: 1.0)
            backgroundLayer.colors = [color1.cgColor, color2.cgColor]
        } else {
            // Серый градиент
            let color1 = NSColor(red: 0.45, green: 0.45, blue: 0.5, alpha: 1.0)
            let color2 = NSColor(red: 0.35, green: 0.35, blue: 0.4, alpha: 1.0)
            backgroundLayer.colors = [color1.cgColor, color2.cgColor]
        }
        
        backgroundLayer.startPoint = CGPoint(x: 0, y: 0)
        backgroundLayer.endPoint = CGPoint(x: 0, y: 1)
        backgroundLayer.cornerRadius = 8
        
        self.layer = backgroundLayer
    }
    
    private func setupLabel(isSmall: Bool) {
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        titleLabel.textColor = NSColor.white
        titleLabel.font = NSFont.systemFont(ofSize: isSmall ? 12 : 14, weight: .medium)
        titleLabel.alignment = .center
        titleLabel.isEditable = false
        titleLabel.isSelectable = false
        titleLabel.isBordered = false
        titleLabel.backgroundColor = NSColor.clear
        
        addSubview(titleLabel)
        
        // Центрируем label
        NSLayoutConstraint.activate([
            titleLabel.centerXAnchor.constraint(equalTo: centerXAnchor),
            titleLabel.centerYAnchor.constraint(equalTo: centerYAnchor),
            titleLabel.leadingAnchor.constraint(greaterThanOrEqualTo: leadingAnchor, constant: 8),
            titleLabel.trailingAnchor.constraint(lessThanOrEqualTo: trailingAnchor, constant: -8)
        ])
    }
    
    private func setupView() {
        // Добавляем tracking area для mouse events
        let trackingArea = NSTrackingArea(
            rect: bounds,
            options: [.mouseEnteredAndExited, .activeInKeyWindow, .inVisibleRect],
            owner: self,
            userInfo: nil
        )
        addTrackingArea(trackingArea)
    }
    
    override func mouseDown(with event: NSEvent) {
        isPressed = true
        // Только изменение цвета, никаких трансформаций
        backgroundLayer.opacity = 0.8
    }

    override func mouseUp(with event: NSEvent) {
        isPressed = false
        backgroundLayer.opacity = 1.0

        // Проверяем, что мышь все еще над кнопкой
        let locationInView = convert(event.locationInWindow, from: nil)
        if bounds.contains(locationInView) {
            // Вызываем action только если мышь над кнопкой
            if let target = target, let action = action {
                _ = target.perform(action, with: self)
            }
        }
    }
    
    override func layout() {
        super.layout()
        // Обновляем размер градиента
        backgroundLayer.frame = bounds
    }
    
    override func updateTrackingAreas() {
        super.updateTrackingAreas()
        
        // Удаляем старые tracking areas
        for trackingArea in trackingAreas {
            removeTrackingArea(trackingArea)
        }
        
        // Добавляем новую
        let trackingArea = NSTrackingArea(
            rect: bounds,
            options: [.mouseEnteredAndExited, .activeInKeyWindow, .inVisibleRect],
            owner: self,
            userInfo: nil
        )
        addTrackingArea(trackingArea)
    }
}
