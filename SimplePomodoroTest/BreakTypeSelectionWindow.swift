import Cocoa

class BreakTypeSelectionWindow: NSWindow {
    
    // Колбэки для действий
    var onShortBreak: (() -> Void)?
    var onLongBreak: (() -> Void)?
    
    // Состояние
    private var completedIntervals: Int = 0
    
    override init(contentRect: NSRect, styleMask style: NSWindow.StyleMask, backing backingStoreType: NSWindow.BackingStoreType, defer flag: Bool) {
        super.init(contentRect: NSRect(x: 0, y: 0, width: 380, height: 100),
                  styleMask: [.borderless],
                  backing: .buffered,
                  defer: false)

        setupWindow()
        setupUI()

        print("🎨 BreakTypeSelectionWindow: Окно выбора типа отдыха создано")
    }

    private func setupWindow() {
        self.level = .floating
        self.isOpaque = false
        self.backgroundColor = NSColor.clear
        self.hasShadow = false // Тень будет у слоя
        self.isMovableByWindowBackground = false

        // Принудительно задаем точный размер как у первого окна
        self.setContentSize(NSSize(width: 380, height: 100))
    }
    
    func configureForIntervals(_ intervals: Int) {
        self.completedIntervals = intervals
        setupUI() // Пересоздаем UI с актуальной информацией
    }
    
    func positionRelativeToStatusItem(statusItemFrame: NSRect) {
        print("🎨 BreakTypeSelectionWindow: Позиционирование возле status item")

        let windowWidth = self.frame.width
        let windowHeight = self.frame.height

        // Позиционируем под status item с небольшим отступом
        let x = statusItemFrame.midX - windowWidth / 2
        let y = statusItemFrame.minY - windowHeight - 8

        self.setFrameOrigin(NSPoint(x: x, y: y))
    }
    
    private func setupUI() {
        print("🎨 BreakTypeSelectionWindow: Создание UI в стиле ModernCompletionWindow")

        // Создаем основной контейнер
        let containerView = NSView()
        containerView.wantsLayer = true
        containerView.translatesAutoresizingMaskIntoConstraints = false

        // Создаем сложный фон с несколькими радиальными градиентами
        setupComplexBackground(for: containerView)

        // Заголовок (по центру, менее жирный)
        let titleLabel = NSTextField(labelWithString: "🤔 Какой отдых выбираете?")
        titleLabel.font = NSFont.systemFont(ofSize: 15, weight: .medium)
        titleLabel.textColor = NSColor.white
        titleLabel.alignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false

        // Создаем красивые градиентные кнопки как "Take a break" (полный размер)
        let shortButton = createGradientButton(title: "Short 17m", color: .green, isSmall: false)
        shortButton.target = self
        shortButton.action = #selector(shortBreakClicked)

        let longButton = createGradientButton(title: "Long 90m", color: .blue, isSmall: false)
        longButton.target = self
        longButton.action = #selector(longBreakClicked)

        // Добавляем все элементы
        containerView.addSubview(titleLabel)
        containerView.addSubview(shortButton)
        containerView.addSubview(longButton)

        self.contentView = containerView

        // Компоновка точно как в первом окне: заголовок по центру сверху, кнопки рядом снизу
        NSLayoutConstraint.activate([
            // Заголовок по центру сверху (компактные отступы как в первом окне)
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 16),
            titleLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            titleLabel.leadingAnchor.constraint(greaterThanOrEqualTo: containerView.leadingAnchor, constant: 15),
            titleLabel.trailingAnchor.constraint(lessThanOrEqualTo: containerView.trailingAnchor, constant: -15),

            // Кнопки рядом снизу (равной ширины, увеличенные отступы с краев)
            // Short Break (слева)
            shortButton.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 16),
            shortButton.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 25),
            shortButton.heightAnchor.constraint(equalToConstant: 32),
            shortButton.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -16),

            // Long Break (справа)
            longButton.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 16),
            longButton.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -25),
            longButton.heightAnchor.constraint(equalToConstant: 32),

            // Равная ширина кнопок с промежутком
            longButton.leadingAnchor.constraint(equalTo: shortButton.trailingAnchor, constant: 10),
            shortButton.widthAnchor.constraint(equalTo: longButton.widthAnchor),

            // Принудительно задаем минимальные размеры контейнера
            containerView.widthAnchor.constraint(greaterThanOrEqualToConstant: 380),
            containerView.heightAnchor.constraint(greaterThanOrEqualToConstant: 100)
        ])

        print("🎨 BreakTypeSelectionWindow: UI создан")
    }
    
    private func createGradientButton(title: String, color: ButtonColor, isSmall: Bool = false) -> NSButton {
        let button = NSButton(title: title, target: nil, action: nil)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.isBordered = false
        button.wantsLayer = true

        // Создаем градиентный слой
        let gradientLayer = CAGradientLayer()

        switch color {
        case .green:
            gradientLayer.colors = [
                NSColor(red: 0.2, green: 0.8, blue: 0.3, alpha: 1.0).cgColor,
                NSColor(red: 0.1, green: 0.6, blue: 0.2, alpha: 1.0).cgColor
            ]
        case .blue:
            gradientLayer.colors = [
                NSColor(red: 0.3, green: 0.6, blue: 1.0, alpha: 1.0).cgColor,
                NSColor(red: 0.2, green: 0.4, blue: 0.8, alpha: 1.0).cgColor
            ]
        case .purple:
            gradientLayer.colors = [
                NSColor(red: 0.7, green: 0.3, blue: 1.0, alpha: 1.0).cgColor,
                NSColor(red: 0.5, green: 0.2, blue: 0.8, alpha: 1.0).cgColor
            ]
        case .darkGray:
            gradientLayer.colors = [
                NSColor(red: 0.4, green: 0.4, blue: 0.4, alpha: 1.0).cgColor,
                NSColor(red: 0.25, green: 0.25, blue: 0.25, alpha: 1.0).cgColor
            ]
        }

        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 0, y: 1)
        gradientLayer.cornerRadius = isSmall ? 6 : 8

        // Тень для кнопки
        gradientLayer.shadowColor = NSColor.black.cgColor
        gradientLayer.shadowOpacity = 0.2
        gradientLayer.shadowOffset = CGSize(width: 0, height: 1)
        gradientLayer.shadowRadius = 3

        button.layer = gradientLayer

        // Белый текст
        let fontSize: CGFloat = isSmall ? 11 : 13
        button.attributedTitle = NSAttributedString(
            string: title,
            attributes: [
                .foregroundColor: NSColor.white,
                .font: NSFont.systemFont(ofSize: fontSize, weight: .semibold)
            ]
        )

        return button
    }

    enum ButtonColor {
        case green, blue, purple, darkGray
    }
    
    private func setupComplexBackground(for view: NSView) {
        // Основной градиентный слой
        let mainGradient = CAGradientLayer()
        mainGradient.colors = [
            NSColor(red: 0.1, green: 0.1, blue: 0.15, alpha: 0.95).cgColor,
            NSColor(red: 0.15, green: 0.15, blue: 0.2, alpha: 0.95).cgColor
        ]
        mainGradient.startPoint = CGPoint(x: 0, y: 0)
        mainGradient.endPoint = CGPoint(x: 1, y: 1)
        mainGradient.cornerRadius = 12

        // Тень
        mainGradient.shadowColor = NSColor.black.cgColor
        mainGradient.shadowOpacity = 0.3
        mainGradient.shadowOffset = CGSize(width: 0, height: 4)
        mainGradient.shadowRadius = 12

        view.layer = mainGradient

        // Добавляем радиальные градиенты для красоты
        DispatchQueue.main.async {
            self.addRadialGradients(to: view)
        }
    }

    private func addRadialGradients(to view: NSView) {
        guard let layer = view.layer else { return }

        // Первый радиальный градиент (левый верхний)
        let radial1 = CAGradientLayer()
        radial1.type = .radial
        radial1.colors = [
            NSColor(red: 0.3, green: 0.4, blue: 0.8, alpha: 0.3).cgColor,
            NSColor.clear.cgColor
        ]
        radial1.startPoint = CGPoint(x: 0.2, y: 0.8)
        radial1.endPoint = CGPoint(x: 0.8, y: 0.2)
        radial1.frame = layer.bounds
        layer.addSublayer(radial1)

        // Второй радиальный градиент (правый нижний)
        let radial2 = CAGradientLayer()
        radial2.type = .radial
        radial2.colors = [
            NSColor(red: 0.8, green: 0.3, blue: 0.6, alpha: 0.2).cgColor,
            NSColor.clear.cgColor
        ]
        radial2.startPoint = CGPoint(x: 0.8, y: 0.2)
        radial2.endPoint = CGPoint(x: 0.2, y: 0.8)
        radial2.frame = layer.bounds
        layer.addSublayer(radial2)
    }
    
    // MARK: - Actions
    
    @objc private func shortBreakClicked() {
        print("🎨 BreakTypeSelectionWindow: Короткий отдых выбран")
        hideWithAnimation {
            self.onShortBreak?()
        }
    }
    
    @objc private func longBreakClicked() {
        print("🎨 BreakTypeSelectionWindow: Длинный отдых выбран")
        hideWithAnimation {
            self.onLongBreak?()
        }
    }
    
    // MARK: - Анимации (точная копия из ModernCompletionWindow)

    func showWithAnimation() {
        print("🎨 BreakTypeSelectionWindow: Показ с анимацией")

        // Начальное состояние
        self.alphaValue = 0
        self.setFrame(NSRect(x: self.frame.origin.x, y: self.frame.origin.y - 20, width: self.frame.width, height: self.frame.height), display: true)

        self.makeKeyAndOrderFront(nil)

        // Анимация появления
        NSAnimationContext.runAnimationGroup { context in
            context.duration = 0.4
            context.timingFunction = CAMediaTimingFunction(name: .easeOut)

            self.animator().alphaValue = 1
            self.animator().setFrame(NSRect(x: self.frame.origin.x, y: self.frame.origin.y + 20, width: self.frame.width, height: self.frame.height), display: true)
        }
    }

    private func hideWithAnimation(completion: @escaping () -> Void) {
        print("🎨 BreakTypeSelectionWindow: Скрытие с анимацией")

        NSAnimationContext.runAnimationGroup({ context in
            context.duration = 0.3
            context.timingFunction = CAMediaTimingFunction(name: .easeIn)

            self.animator().alphaValue = 0
            self.animator().setFrame(NSRect(x: self.frame.origin.x, y: self.frame.origin.y + 15, width: self.frame.width, height: self.frame.height), display: true)
        }) {
            self.orderOut(nil)
            completion()
        }
    }
}
