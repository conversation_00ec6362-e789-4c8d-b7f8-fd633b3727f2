import Foundation
import UserNotifications

class MotivationManager {
    private let statisticsManager: StatisticsManager
    private let analyzer: WorkPatternAnalyzer
    
    init(statisticsManager: StatisticsManager, analyzer: WorkPatternAnalyzer) {
        self.statisticsManager = statisticsManager
        self.analyzer = analyzer
        
        requestNotificationPermission()
    }
    
    // MARK: - Уведомления
    
    private func requestNotificationPermission() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
            if granted {
                print("🔔 MotivationManager: Разрешение на уведомления получено")
            } else {
                print("❌ MotivationManager: Разрешение на уведомления отклонено")
            }
        }
    }
    
    func checkAndSendMotivationalNotification() {
        let pattern = analyzer.analyzeWorkPattern(for: .lastWeek)
        
        // Проверяем различные сценарии для мотивационных уведомлений
        if shouldSendStreakNotification() {
            sendStreakNotification()
        } else if shouldSendEncouragementNotification(pattern) {
            sendEncouragementNotification(pattern)
        } else if shouldSendWarningNotification(pattern) {
            sendWarningNotification(pattern)
        }
    }
    
    // MARK: - Проверки условий для уведомлений
    
    private func shouldSendStreakNotification() -> Bool {
        let today = statisticsManager.getStatsForToday()
        let yesterday = statisticsManager.getStatsForYesterday()
        
        // Если сегодня и вчера работали
        return today > 0 && yesterday > 0
    }
    
    private func shouldSendEncouragementNotification(_ pattern: WorkPattern) -> Bool {
        // Если есть улучшения в паттерне
        return pattern.consistencyScore > 0.7 && pattern.workingDaysPerWeek >= 4
    }
    
    private func shouldSendWarningNotification(_ pattern: WorkPattern) -> Bool {
        // Если есть серьезные риски
        return pattern.riskFactors.contains { risk in
            switch risk {
            case .overwork, .burnoutRisk:
                return true
            default:
                return false
            }
        }
    }
    
    // MARK: - Отправка уведомлений
    
    private func sendStreakNotification() {
        let content = UNMutableNotificationContent()
        content.title = "🔥 Отличная работа!"
        content.body = "Вы работаете второй день подряд! Продолжайте в том же духе."
        content.sound = .default
        
        let request = UNNotificationRequest(
            identifier: "streak_notification",
            content: content,
            trigger: nil
        )
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("❌ MotivationManager: Ошибка отправки уведомления: \(error)")
            }
        }
    }
    
    private func sendEncouragementNotification(_ pattern: WorkPattern) {
        let content = UNMutableNotificationContent()
        content.title = "📈 Прогресс заметен!"
        content.body = generateEncouragementMessage(pattern)
        content.sound = .default
        
        let request = UNNotificationRequest(
            identifier: "encouragement_notification",
            content: content,
            trigger: nil
        )
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("❌ MotivationManager: Ошибка отправки уведомления: \(error)")
            }
        }
    }
    
    private func sendWarningNotification(_ pattern: WorkPattern) {
        let content = UNMutableNotificationContent()
        content.title = "⚠️ Внимание!"
        content.body = generateWarningMessage(pattern)
        content.sound = .default
        
        let request = UNNotificationRequest(
            identifier: "warning_notification",
            content: content,
            trigger: nil
        )
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("❌ MotivationManager: Ошибка отправки уведомления: \(error)")
            }
        }
    }
    
    // MARK: - Генерация сообщений
    
    private func generateEncouragementMessage(_ pattern: WorkPattern) -> String {
        let messages = [
            "Ваша стабильность работы составляет \(Int(pattern.consistencyScore * 100))%!",
            "Вы работаете \(pattern.workingDaysPerWeek) дней в неделю - отличный результат!",
            "Ваш рабочий ритм становится все более устойчивым.",
            "Продолжайте поддерживать такой темп работы!"
        ]
        
        return messages.randomElement() ?? "Отличная работа!"
    }
    
    private func generateWarningMessage(_ pattern: WorkPattern) -> String {
        for risk in pattern.riskFactors {
            switch risk {
            case .overwork(let average):
                return "Вы работаете \(String(format: "%.1f", average)) интервалов в день. Не забывайте об отдыхе!"
            case .burnoutRisk:
                return "Обнаружен риск выгорания. Рекомендуем более равномерно распределить нагрузку."
            default:
                continue
            }
        }
        
        return "Обратите внимание на свой рабочий режим."
    }
    
    // MARK: - Мотивационные цитаты
    
    func getRandomMotivationalQuote() -> String {
        let quotes = [
            "Постоянство - мать успеха. Лучше работать понемногу каждый день, чем много раз в неделю.",
            "Качество важнее количества. Сосредоточьтесь на продуктивности, а не на часах.",
            "Отдых - это не роскошь, а необходимость для устойчивой продуктивности.",
            "Маленькие шаги каждый день приводят к большим результатам.",
            "Ваш будущий успех создается сегодняшними привычками.",
            "Баланс между работой и отдыхом - ключ к долгосрочной продуктивности.",
            "Не сравнивайте свой прогресс с другими. Сравнивайте себя с собой вчерашним.",
            "Каждый завершенный интервал - это шаг к вашей цели.",
            "Регулярность побеждает интенсивность.",
            "Помните: вы строите не спринт, а марафон продуктивности."
        ]
        
        return quotes.randomElement() ?? "Продолжайте двигаться вперед!"
    }
    
    // MARK: - Анализ прогресса
    
    func getProgressInsight() -> String {
        let today = statisticsManager.getStatsForToday()
        let week = statisticsManager.getStatsForCurrentWeek()
        let pattern = analyzer.analyzeWorkPattern(for: .lastWeek)
        
        if today == 0 {
            return "Сегодня еще не было интервалов. Самое время начать!"
        }
        
        if pattern.consistencyScore > 0.8 {
            return "Ваша стабильность работы на высоком уровне (\(Int(pattern.consistencyScore * 100))%). Отличная работа!"
        }
        
        if week > 20 {
            return "На этой неделе уже \(week) интервалов. Не забывайте об отдыхе!"
        }
        
        if pattern.workingDaysPerWeek >= 5 {
            return "Вы работаете \(pattern.workingDaysPerWeek) дней в неделю. Превосходная регулярность!"
        }
        
        return "Продолжайте работать над формированием устойчивых привычек."
    }
    
    // MARK: - Рекомендации по времени
    
    func getTimeBasedRecommendation() -> String? {
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: Date())
        let today = statisticsManager.getStatsForToday()
        
        switch hour {
        case 9...11:
            if today == 0 {
                return "Утро - отличное время для начала продуктивного дня!"
            }
        case 12...14:
            if today < 2 {
                return "После обеда можно продолжить работу с новыми силами."
            }
        case 15...17:
            if today > 6 {
                return "Вы уже много поработали сегодня. Возможно, стоит сделать перерыв?"
            }
        case 18...20:
            if today > 8 {
                return "Рабочий день подходит к концу. Время подумать об отдыхе."
            }
        default:
            break
        }
        
        return nil
    }
}
