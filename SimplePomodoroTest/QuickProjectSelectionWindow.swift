import Cocoa

/// Простой выпадающий список проектов поверх окна
class QuickProjectSelectionWindow: NSWindow {

    // Колбэки
    var onProjectSelected: ((UUID) -> Void)?
    var onCancel: (() -> Void)?

    // Данные
    private let projectManager: ProjectManager
    private var projects: [Project] = []
    private var projectIdMap: [Int: UUID] = [:]

    // UI элементы
    private var containerView: NSView!

    init(projectManager: ProjectManager) {
        self.projectManager = projectManager

        super.init(contentRect: NSRect(x: 0, y: 0, width: 200, height: 200),
                  styleMask: [.borderless],
                  backing: .buffered,
                  defer: false)

        setupWindow()
        loadProjects()
        setupUI()
    }
    
    private func setupWindow() {
        self.level = .popUpMenu
        self.backgroundColor = NSColor.clear
        self.isOpaque = false
        self.hasShadow = true
        self.isMovableByWindowBackground = false

        // Закрытие при клике вне окна
        self.hidesOnDeactivate = true
    }
    
    private func loadProjects() {
        // Получаем избранные проекты + последние использованные
        let favoriteProjects = projectManager.getFavoriteProjects()
        let recentProjects = projectManager.getRecentProjects(limit: 5)
        
        // Объединяем и убираем дубликаты
        var allProjects = favoriteProjects
        for recent in recentProjects {
            if !allProjects.contains(where: { $0.id == recent.id }) {
                allProjects.append(recent)
            }
        }
        
        // Ограничиваем до 8 проектов для компактности
        projects = Array(allProjects.prefix(8))
    }
    
    private func setupUI() {
        // Простой контейнер с серым полупрозрачным фоном
        containerView = NSView()
        containerView.translatesAutoresizingMaskIntoConstraints = false
        containerView.wantsLayer = true
        containerView.layer?.backgroundColor = NSColor.controlBackgroundColor.withAlphaComponent(0.95).cgColor
        containerView.layer?.cornerRadius = 8
        containerView.layer?.borderWidth = 1
        containerView.layer?.borderColor = NSColor.separatorColor.withAlphaComponent(0.5).cgColor

        self.contentView?.addSubview(containerView)

        // Создаем кнопки проектов
        createProjectButtons()

        // Constraints - контейнер заполняет окно
        NSLayoutConstraint.activate([
            containerView.topAnchor.constraint(equalTo: self.contentView!.topAnchor),
            containerView.leadingAnchor.constraint(equalTo: self.contentView!.leadingAnchor),
            containerView.trailingAnchor.constraint(equalTo: self.contentView!.trailingAnchor),
            containerView.bottomAnchor.constraint(equalTo: self.contentView!.bottomAnchor),
        ])
    }
    
    private func createProjectButtons() {
        var previousButton: NSButton?
        let buttonHeight: CGFloat = 28
        let spacing: CGFloat = 2
        let padding: CGFloat = 8

        for (index, project) in projects.enumerated() {
            let button = createProjectButton(for: project)
            containerView.addSubview(button)

            NSLayoutConstraint.activate([
                button.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: padding),
                button.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -padding),
                button.heightAnchor.constraint(equalToConstant: buttonHeight),
            ])

            if let previous = previousButton {
                button.topAnchor.constraint(equalTo: previous.bottomAnchor, constant: spacing).isActive = true
            } else {
                button.topAnchor.constraint(equalTo: containerView.topAnchor, constant: padding).isActive = true
            }

            // Последняя кнопка определяет высоту контейнера
            if index == projects.count - 1 {
                containerView.bottomAnchor.constraint(equalTo: button.bottomAnchor, constant: padding).isActive = true
            }

            previousButton = button
        }

        // Обновляем размер окна под количество проектов
        let totalHeight = CGFloat(projects.count) * buttonHeight + CGFloat(projects.count - 1) * spacing + padding * 2
        let windowFrame = NSRect(x: 0, y: 0, width: 200, height: min(totalHeight, 200)) // Максимум 200px высоты
        self.setFrame(windowFrame, display: false)

        // Если нет проектов, показываем сообщение
        if projects.isEmpty {
            let noProjectsLabel = NSTextField(labelWithString: "No projects")
            noProjectsLabel.font = NSFont.systemFont(ofSize: 11)
            noProjectsLabel.textColor = NSColor.secondaryLabelColor
            noProjectsLabel.alignment = .center
            noProjectsLabel.translatesAutoresizingMaskIntoConstraints = false

            containerView.addSubview(noProjectsLabel)

            NSLayoutConstraint.activate([
                noProjectsLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
                noProjectsLabel.centerYAnchor.constraint(equalTo: containerView.centerYAnchor),
                containerView.heightAnchor.constraint(equalToConstant: 40)
            ])

            self.setFrame(NSRect(x: 0, y: 0, width: 200, height: 40), display: false)
        }
    }
    
    private func createProjectButton(for project: Project) -> NSButton {
        let emoji = project.customEmoji?.isEmpty == false ? project.customEmoji! : project.type.emoji
        let title = "\(emoji) \(project.name)"

        let button = NSButton(title: title, target: self, action: #selector(projectButtonClicked(_:)))
        button.translatesAutoresizingMaskIntoConstraints = false
        button.isBordered = false
        button.wantsLayer = true

        // Используем hashValue как tag и сохраняем соответствие
        let tagValue = project.id.hashValue
        button.tag = tagValue
        projectIdMap[tagValue] = project.id

        // Простой стиль - прозрачная кнопка с белым текстом
        button.layer?.backgroundColor = NSColor.clear.cgColor
        button.layer?.cornerRadius = 4

        // Темный текст на светлом фоне
        button.attributedTitle = NSAttributedString(
            string: title,
            attributes: [
                .foregroundColor: NSColor.labelColor,
                .font: NSFont.systemFont(ofSize: 13, weight: .medium)
            ]
        )

        // Hover эффект
        button.layer?.borderWidth = 0

        return button
    }
    

    
    // MARK: - Actions
    
    @objc private func projectButtonClicked(_ sender: NSButton) {
        guard let projectId = projectIdMap[sender.tag] else { return }

        hideWithAnimation {
            self.onProjectSelected?(projectId)
        }
    }
    

    
    // MARK: - Позиционирование

    func positionRelativeToBreakEndWindow(_ breakEndWindow: NSWindow) {
        let breakWindowFrame = breakEndWindow.frame
        let windowSize = self.frame.size

        // Позиционируем по центру над окном завершения перерыва
        let windowOrigin = NSPoint(
            x: breakWindowFrame.midX - windowSize.width / 2,
            y: breakWindowFrame.midY - windowSize.height / 2
        )

        self.setFrameOrigin(windowOrigin)
    }
    
    // MARK: - Анимации
    
    func showWithAnimation() {
        self.alphaValue = 0.0
        self.makeKeyAndOrderFront(nil)
        
        NSAnimationContext.runAnimationGroup({ context in
            context.duration = 0.2
            context.timingFunction = CAMediaTimingFunction(name: .easeOut)
            self.animator().alphaValue = 1.0
        })
    }
    
    private func hideWithAnimation(completion: @escaping () -> Void) {
        NSAnimationContext.runAnimationGroup({ context in
            context.duration = 0.15
            context.timingFunction = CAMediaTimingFunction(name: .easeIn)
            self.animator().alphaValue = 0.0
        }, completionHandler: {
            self.orderOut(nil)
            completion()
        })
    }
}
