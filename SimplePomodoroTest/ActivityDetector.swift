import Cocoa

/// Детектор активности пользователя для отслеживания качества отдыха
class ActivityDetector {
    
    // MARK: - Properties
    
    private var isMonitoring = false
    private var lastActivityTime = Date()
    private var activityThreshold: TimeInterval = 1.0  // Минимум 1 секунда активности
    
    // Мониторы событий
    private var mouseMonitor: Any?
    private var keyboardMonitor: Any?
    
    // MARK: - Public Methods
    
    /// Начинает мониторинг активности пользователя
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        print("🔍 ActivityDetector: Начинаем мониторинг активности")
        isMonitoring = true
        lastActivityTime = Date()
        
        // Мониторинг движений мыши (только значительные движения)
        mouseMonitor = NSEvent.addGlobalMonitorForEvents(matching: [.mouseMoved, .leftMouseDown, .rightMouseDown, .scrollWheel]) { [weak self] event in
            self?.handleMouseActivity(event)
        }
        
        // Мониторинг клавиатуры
        keyboardMonitor = NSEvent.addGlobalMonitorForEvents(matching: [.keyDown]) { [weak self] event in
            self?.handleKeyboardActivity(event)
        }
    }
    
    /// Останавливает мониторинг активности
    func stopMonitoring() {
        guard isMonitoring else { return }
        
        print("🔍 ActivityDetector: Останавливаем мониторинг активности")
        isMonitoring = false
        
        if let mouseMonitor = mouseMonitor {
            NSEvent.removeMonitor(mouseMonitor)
            self.mouseMonitor = nil
        }
        
        if let keyboardMonitor = keyboardMonitor {
            NSEvent.removeMonitor(keyboardMonitor)
            self.keyboardMonitor = nil
        }
    }
    
    /// Проверяет, была ли активность в последнюю минуту
    func wasActiveInLastMinute() -> Bool {
        let oneMinuteAgo = Date().addingTimeInterval(-60)
        let wasActive = lastActivityTime > oneMinuteAgo
        
        print("🔍 ActivityDetector: Проверка активности за последнюю минуту: \(wasActive ? "ДА" : "НЕТ")")
        return wasActive
    }
    
    /// Сбрасывает время последней активности (для начала нового отдыха)
    func resetActivity() {
        lastActivityTime = Date().addingTimeInterval(-120) // 2 минуты назад
        print("🔍 ActivityDetector: Сброс активности")
    }
    
    // MARK: - Private Methods
    
    private func handleMouseActivity(_ event: NSEvent) {
        // Фильтруем только значительные движения мыши
        if event.type == .mouseMoved {
            // Проверяем, что движение достаточно значительное
            let deltaX = abs(event.deltaX)
            let deltaY = abs(event.deltaY)
            
            // Игнорируем микро-движения (случайные толчки мыши)
            if deltaX < 5 && deltaY < 5 {
                return
            }
        }
        
        markActivity()
    }
    
    private func handleKeyboardActivity(_ event: NSEvent) {
        markActivity()
    }
    
    private func markActivity() {
        let now = Date()
        let timeSinceLastActivity = now.timeIntervalSince(lastActivityTime)
        
        // Обновляем время последней активности только если прошло достаточно времени
        if timeSinceLastActivity >= activityThreshold {
            lastActivityTime = now
            print("🔍 ActivityDetector: Зафиксирована активность")
        }
    }
    
    deinit {
        stopMonitoring()
    }
}
