#!/bin/bash

# Простой скрипт для обновления версии из todo.md

TODO_FILE="todo.md"
VERSION_CONFIG="Version.xcconfig"

if [ -f "$TODO_FILE" ]; then
    # Извлекаем версию из todo.md
    VERSION=$(grep "APP_VERSION" "$TODO_FILE" | sed 's/.*APP_VERSION = \([0-9.]*\).*/\1/')
    
    if [ ! -z "$VERSION" ]; then
        echo "📦 Обновляем версию на $VERSION"
        
        # Обновляем Version.xcconfig
        echo "// Version configuration file" > "$VERSION_CONFIG"
        echo "// This file defines the app version" >> "$VERSION_CONFIG"
        echo "" >> "$VERSION_CONFIG"
        echo "MARKETING_VERSION = $VERSION" >> "$VERSION_CONFIG"

        # Также обновляем project.pbxproj
        PROJECT_FILE="SimplePomodoroTest.xcodeproj/project.pbxproj"
        if [ -f "$PROJECT_FILE" ]; then
            sed -i '' "s/MARKETING_VERSION = [0-9.]*;/MARKETING_VERSION = $VERSION;/g" "$PROJECT_FILE"
            echo "✅ Версия обновлена в $VERSION_CONFIG и project.pbxproj"
        else
            echo "✅ Версия обновлена в $VERSION_CONFIG"
        fi
    else
        echo "⚠️ Версия не найдена в todo.md"
        exit 1
    fi
else
    echo "❌ Файл todo.md не найден"
    exit 1
fi
