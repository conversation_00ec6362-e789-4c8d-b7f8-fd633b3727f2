#!/bin/bash

# Скрипт для сборки uProd с улучшенной системой логирования

echo "🔨 Сборка uProd с системой логирования для диагностики крашей..."

# Проверяем, что мы в правильной директории
if [ ! -f "SimplePomodoroTest.xcodeproj/project.pbxproj" ]; then
    echo "❌ Ошибка: Не найден файл проекта SimplePomodoroTest.xcodeproj"
    echo "Убедитесь, что вы запускаете скрипт из корневой директории проекта"
    exit 1
fi

# Очищаем предыдущие сборки
echo "🧹 Очистка предыдущих сборок..."
xcodebuild clean -project SimplePomodoroTest.xcodeproj -scheme SimplePomodoroTest -configuration Release

# Собираем проект
echo "🔨 Сборка проекта..."
xcodebuild -project SimplePomodoroTest.xcodeproj -scheme SimplePomodoroTest -configuration Release build

# Проверяем результат сборки
if [ $? -eq 0 ]; then
    echo "✅ Сборка завершена успешно!"
    
    # Находим собранное приложение
    BUILD_DIR=$(xcodebuild -project SimplePomodoroTest.xcodeproj -scheme SimplePomodoroTest -configuration Release -showBuildSettings | grep "BUILT_PRODUCTS_DIR" | awk '{print $3}')
    APP_PATH="$BUILD_DIR/uProd.app"
    
    if [ -d "$APP_PATH" ]; then
        echo "📱 Приложение собрано: $APP_PATH"
        
        # Показываем размер приложения
        APP_SIZE=$(du -sh "$APP_PATH" | cut -f1)
        echo "📏 Размер приложения: $APP_SIZE"
        
        # Проверяем, что новые файлы включены в сборку
        echo "🔍 Проверка включения новых файлов логирования..."
        
        if [ -f "$APP_PATH/Contents/MacOS/uProd" ]; then
            # Проверяем наличие символов логирования в бинарном файле
            if strings "$APP_PATH/Contents/MacOS/uProd" | grep -q "Logger"; then
                echo "✅ Система логирования включена в сборку"
            else
                echo "⚠️  Предупреждение: Символы логирования не найдены в бинарном файле"
            fi
            
            if strings "$APP_PATH/Contents/MacOS/uProd" | grep -q "CrashHandler"; then
                echo "✅ Обработчик крашей включен в сборку"
            else
                echo "⚠️  Предупреждение: Символы обработчика крашей не найдены"
            fi
        fi
        
        echo ""
        echo "🚀 Готово к тестированию!"
        echo ""
        echo "Для установки приложения выполните:"
        echo "cp -r \"$APP_PATH\" \"/Applications/\""
        echo ""
        echo "Логи будут сохраняться в:"
        echo "~/Library/Application Support/uProd/Logs/"
        echo ""
        echo "Для просмотра логов в реальном времени:"
        echo "tail -f ~/Library/Application\\ Support/uProd/Logs/uProd_*.log"
        
    else
        echo "❌ Ошибка: Собранное приложение не найдено по пути $APP_PATH"
        exit 1
    fi
    
else
    echo "❌ Ошибка сборки!"
    echo ""
    echo "Возможные причины:"
    echo "1. Отсутствуют новые файлы в проекте Xcode"
    echo "2. Синтаксические ошибки в коде"
    echo "3. Проблемы с зависимостями"
    echo ""
    echo "Для добавления новых файлов в проект:"
    echo "1. Откройте SimplePomodoroTest.xcodeproj в Xcode"
    echo "2. Добавьте файлы Logger.swift, CrashHandler.swift, LogViewer.swift"
    echo "3. Убедитесь, что они включены в target SimplePomodoroTest"
    exit 1
fi
