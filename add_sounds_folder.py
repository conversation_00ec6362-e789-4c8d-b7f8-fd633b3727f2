#!/usr/bin/env python3

import os
import uuid
import re

def add_sounds_folder_to_xcode_project():
    """Добавляет папку Sounds как folder reference в проект Xcode"""
    
    project_file = "SimplePomodoroTest.xcodeproj/project.pbxproj"
    
    # Читаем файл проекта
    with open(project_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("📁 Добавляем папку Sounds как folder reference...")
    
    # Генерируем UUID для папки Sounds
    sounds_folder_uuid = str(uuid.uuid4()).replace('-', '').upper()[:24]
    sounds_build_file_uuid = str(uuid.uuid4()).replace('-', '').upper()[:24]
    
    print(f"🔧 UUID для папки Sounds: {sounds_folder_uuid}")
    print(f"🔧 UUID для build file: {sounds_build_file_uuid}")
    
    # Добавляем PBXFileReference для папки Sounds как folder reference
    file_ref_section = f'\t\t{sounds_folder_uuid} /* Sounds */ = {{isa = PBXFileReference; lastKnownFileType = folder; path = Sounds; sourceTree = "<group>"; }};\n'
    
    # Добавляем PBXBuildFile для папки Sounds
    build_file_section = f'\t\t{sounds_build_file_uuid} /* Sounds in Resources */ = {{isa = PBXBuildFile; fileRef = {sounds_folder_uuid} /* Sounds */; }};\n'
    
    # Находим секцию PBXFileReference и добавляем нашу папку
    file_ref_pattern = r'(/\* Begin PBXFileReference section \*/.*?)(/\* End PBXFileReference section \*/)'
    if re.search(file_ref_pattern, content, re.DOTALL):
        content = re.sub(file_ref_pattern, r'\1' + file_ref_section + r'\t\t\2', content, flags=re.DOTALL)
        print("✅ Добавлена PBXFileReference секция")
    else:
        print("❌ Не найдена секция PBXFileReference")
        return False
    
    # Находим секцию PBXBuildFile и добавляем наш build file
    build_file_pattern = r'(/\* Begin PBXBuildFile section \*/.*?)(/\* End PBXBuildFile section \*/)'
    if re.search(build_file_pattern, content, re.DOTALL):
        content = re.sub(build_file_pattern, r'\1' + build_file_section + r'\t\t\2', content, flags=re.DOTALL)
        print("✅ Добавлена PBXBuildFile секция")
    else:
        print("❌ Не найдена секция PBXBuildFile")
        return False
    
    # Находим PBXResourcesBuildPhase и добавляем наш файл
    resources_pattern = r'(/\* Begin PBXResourcesBuildPhase section \*/.*?files = \(\s*)(.*?)(\s*\);)'
    match = re.search(resources_pattern, content, re.DOTALL)
    if match:
        existing_files = match.group(2)
        new_file = f'\t\t\t\t{sounds_build_file_uuid} /* Sounds in Resources */,\n'
        
        content = re.sub(resources_pattern, r'\1' + existing_files + new_file + r'\3', content, flags=re.DOTALL)
        print("✅ Добавлен файл в PBXResourcesBuildPhase")
    else:
        print("❌ Не найдена секция PBXResourcesBuildPhase")
        return False
    
    # Сохраняем изменения
    with open(project_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Папка Sounds успешно добавлена в проект как folder reference!")
    print("📁 Теперь структура папок будет сохраняться при сборке")
    print("🔨 Можно собирать проект обычной командой:")
    print("xcodebuild -project SimplePomodoroTest.xcodeproj -scheme SimplePomodoroTest -configuration Release build")
    
    return True

if __name__ == "__main__":
    add_sounds_folder_to_xcode_project()
