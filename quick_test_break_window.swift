#!/usr/bin/env swift

import Cocoa

// Быстрый тест для окна отдыха - без ожидания!
// Запуск: swift quick_test_break_window.swift

// Включаем код BreakStartWindow прямо здесь
class BreakStartWindow: NSWindow {
    override init(contentRect: NSRect, styleMask style: NSWindow.StyleMask, backing backingStoreType: NSWindow.BackingStoreType, defer flag: Bool) {
        super.init(contentRect: NSRect(x: 0, y: 0, width: 600, height: 200), // Увеличили размер
                   styleMask: [.titled, .closable],
                   backing: .buffered,
                   defer: false)

        self.title = "Время отдыха!"
        self.isReleasedWhenClosed = false
        self.level = .floating

        setupUI()
    }

    func setupUI() {
        let containerView = NSView()
        containerView.wantsLayer = true

        // Создаем градиентный фон
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            NSColor(red: 0.2, green: 0.8, blue: 0.6, alpha: 1.0).cgColor,
            NSColor(red: 0.1, green: 0.6, blue: 0.4, alpha: 1.0).cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 1)
        containerView.layer?.addSublayer(gradientLayer)

        // Создаем колонки
        let leftColumn = NSView()
        let rightColumn = NSView()

        leftColumn.translatesAutoresizingMaskIntoConstraints = false
        rightColumn.translatesAutoresizingMaskIntoConstraints = false

        // Предупреждение об активности
        let activityWarningLabel = NSTextField(labelWithString: "⚠️ Обнаружена активность")
        activityWarningLabel.font = NSFont.systemFont(ofSize: 12, weight: .medium)
        activityWarningLabel.textColor = NSColor.white
        activityWarningLabel.translatesAutoresizingMaskIntoConstraints = false

        // Заголовок
        let titleLabel = NSTextField(labelWithString: "🌿 Время отдыха!")
        titleLabel.font = NSFont.systemFont(ofSize: 24, weight: .bold)
        titleLabel.textColor = NSColor.white
        titleLabel.translatesAutoresizingMaskIntoConstraints = false

        // Подзаголовок
        let subtitleLabel = NSTextField(labelWithString: "Закройте ноутбук и отдохните")
        subtitleLabel.font = NSFont.systemFont(ofSize: 14, weight: .regular)
        subtitleLabel.textColor = NSColor.white
        subtitleLabel.translatesAutoresizingMaskIntoConstraints = false

        // Кнопка "Иду отдыхать"
        let okButton = NSButton(title: "  Иду отдыхать  ", target: nil, action: nil) // Добавляем пробелы для внутренних отступов
        okButton.bezelStyle = .rounded
        okButton.translatesAutoresizingMaskIntoConstraints = false

        // Таймер
        let timeLabel = NSTextField(labelWithString: "15:52")
        timeLabel.font = NSFont.monospacedDigitSystemFont(ofSize: 48, weight: .bold)
        timeLabel.textColor = NSColor.white
        timeLabel.alignment = .center
        timeLabel.translatesAutoresizingMaskIntoConstraints = false

        // Кнопка "Я отдыхаю за компом" (красная)
        let hideButton = NSButton(title: "  Я отдыхаю за компом  ", target: nil, action: nil) // Добавляем пробелы для внутренних отступов
        hideButton.bezelStyle = .rounded
        hideButton.contentTintColor = NSColor.red
        hideButton.translatesAutoresizingMaskIntoConstraints = false

        // Добавляем элементы в колонки
        leftColumn.addSubview(activityWarningLabel)
        leftColumn.addSubview(titleLabel)
        leftColumn.addSubview(subtitleLabel)
        leftColumn.addSubview(okButton)

        rightColumn.addSubview(timeLabel)
        rightColumn.addSubview(hideButton)

        // Добавляем колонки в контейнер
        containerView.addSubview(leftColumn)
        containerView.addSubview(rightColumn)

        self.contentView = containerView

        // Настройка constraints
        NSLayoutConstraint.activate([
            // Основные колонки
            leftColumn.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 16),
            leftColumn.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            leftColumn.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -16),
            leftColumn.widthAnchor.constraint(equalTo: containerView.widthAnchor, multiplier: 0.55),

            rightColumn.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 16),
            rightColumn.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),
            rightColumn.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -16),
            rightColumn.leadingAnchor.constraint(equalTo: leftColumn.trailingAnchor, constant: 16),

            // Элементы левой колонки
            activityWarningLabel.topAnchor.constraint(equalTo: leftColumn.topAnchor),
            activityWarningLabel.leadingAnchor.constraint(equalTo: leftColumn.leadingAnchor),
            activityWarningLabel.trailingAnchor.constraint(equalTo: leftColumn.trailingAnchor),

            titleLabel.topAnchor.constraint(equalTo: activityWarningLabel.bottomAnchor, constant: 12),
            titleLabel.leadingAnchor.constraint(equalTo: leftColumn.leadingAnchor),
            titleLabel.trailingAnchor.constraint(equalTo: leftColumn.trailingAnchor),

            subtitleLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 6),
            subtitleLabel.leadingAnchor.constraint(equalTo: leftColumn.leadingAnchor),
            subtitleLabel.trailingAnchor.constraint(equalTo: leftColumn.trailingAnchor),

            okButton.bottomAnchor.constraint(equalTo: leftColumn.bottomAnchor, constant: -50),
            okButton.leadingAnchor.constraint(equalTo: leftColumn.leadingAnchor),
            okButton.widthAnchor.constraint(equalToConstant: 140),
            okButton.heightAnchor.constraint(equalToConstant: 38),

            // Элементы правой колонки
            timeLabel.centerYAnchor.constraint(equalTo: rightColumn.centerYAnchor, constant: -10),
            timeLabel.leadingAnchor.constraint(equalTo: rightColumn.leadingAnchor),
            timeLabel.trailingAnchor.constraint(equalTo: rightColumn.trailingAnchor),
            timeLabel.heightAnchor.constraint(equalToConstant: 80),

            hideButton.bottomAnchor.constraint(equalTo: rightColumn.bottomAnchor, constant: -50),
            hideButton.centerXAnchor.constraint(equalTo: rightColumn.centerXAnchor),
            hideButton.heightAnchor.constraint(equalToConstant: 38),
        ])

        // Обновляем размеры градиента
        DispatchQueue.main.async {
            gradientLayer.frame = containerView.bounds
        }
    }
}

class QuickTestApp: NSObject, NSApplicationDelegate {
    var breakWindow: BreakStartWindow?
    
    func applicationDidFinishLaunching(_ notification: Notification) {
        print("🚀 БЫСТРЫЙ ТЕСТ: Показываем окно отдыха сразу!")
        
        showBreakWindow()
        
        // Горячая клавиша для повторного показа
        setupHotkey()
    }
    
    func showBreakWindow() {
        breakWindow?.close()
        breakWindow = BreakStartWindow()
        breakWindow?.makeKeyAndOrderFront(nil)
        breakWindow?.center()
        
        print("✅ Окно отдыха показано!")
        print("📏 Размер: \(breakWindow?.frame.size ?? CGSize.zero)")
        print("⌨️  Нажмите Cmd+T для повторного показа")
        print("⌨️  Нажмите Cmd+Q для выхода")
    }
    
    func setupHotkey() {
        // Добавляем горячую клавишу Cmd+T
        let hotkey = NSMenuItem()
        hotkey.keyEquivalent = "t"
        hotkey.keyEquivalentModifierMask = .command
        hotkey.target = self
        hotkey.action = #selector(hotkeyPressed)
        
        let menu = NSMenu()
        menu.addItem(hotkey)
        NSApplication.shared.mainMenu = menu
    }
    
    @objc func hotkeyPressed() {
        print("🔥 Горячая клавиша! Показываем окно заново...")
        showBreakWindow()
    }
}

// Запуск
let app = NSApplication.shared
let delegate = QuickTestApp()
app.delegate = delegate
app.run()
