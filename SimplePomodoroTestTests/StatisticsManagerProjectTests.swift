import XCTest
@testable import uProd

class StatisticsManagerProjectTests: XCTestCase {
    
    var statisticsManager: StatisticsManager!
    var projectManager: ProjectManager!
    
    override func setUp() {
        super.setUp()
        statisticsManager = StatisticsManager()
        projectManager = ProjectManager()
        
        // Очищаем данные перед каждым тестом
        statisticsManager.clearAllIntervals()
        projectManager.clearAllData()
    }
    
    override func tearDown() {
        // Очищаем данные после каждого теста
        statisticsManager.clearAllIntervals()
        projectManager.clearAllData()
        statisticsManager = nil
        projectManager = nil
        super.tearDown()
    }
    
    // MARK: - Work/Personal Statistics Tests
    
    func testWorkPersonalStatisticsToday() {
        // Создаем проекты
        let workProject = projectManager.createProject(name: "Work", type: .work, isWorkRelated: true)
        let personalProject = projectManager.createProject(name: "Personal", type: .personal, isWorkRelated: false)
        
        // Записываем интервалы
        statisticsManager.recordCompletedInterval(duration: 1500, projectId: workProject.id)
        statisticsManager.recordCompletedInterval(duration: 1500, projectId: workProject.id)
        statisticsManager.recordCompletedInterval(duration: 1500, projectId: personalProject.id)
        
        let stats = statisticsManager.getWorkPersonalStatisticsToday(projectManager: projectManager)
        
        XCTAssertEqual(stats.work, 2)
        XCTAssertEqual(stats.personal, 1)
    }
    
    func testWorkPersonalStatisticsWithLegacyIntervals() {
        // Создаем проекты
        let workProject = projectManager.createProject(name: "Work", type: .work, isWorkRelated: true)
        
        // Записываем интервалы с проектом и без проекта (legacy)
        statisticsManager.recordCompletedInterval(duration: 1500, projectId: workProject.id)
        statisticsManager.recordCompletedInterval(duration: 1500, projectId: nil) // legacy interval
        
        let stats = statisticsManager.getWorkPersonalStatisticsToday(projectManager: projectManager)
        
        // Legacy интервалы должны считаться рабочими
        XCTAssertEqual(stats.work, 2)
        XCTAssertEqual(stats.personal, 0)
    }
    
    func testWorkPersonalStatisticsCurrentWeek() {
        let workProject = projectManager.createProject(name: "Work", type: .work, isWorkRelated: true)
        let personalProject = projectManager.createProject(name: "Personal", type: .personal, isWorkRelated: false)
        
        // Записываем интервалы
        statisticsManager.recordCompletedInterval(duration: 1500, projectId: workProject.id)
        statisticsManager.recordCompletedInterval(duration: 1500, projectId: personalProject.id)
        statisticsManager.recordCompletedInterval(duration: 1500, projectId: personalProject.id)
        
        let stats = statisticsManager.getWorkPersonalStatisticsCurrentWeek(projectManager: projectManager)
        
        XCTAssertEqual(stats.work, 1)
        XCTAssertEqual(stats.personal, 2)
    }
    
    func testWorkPersonalStatisticsCurrentMonth() {
        let workProject = projectManager.createProject(name: "Work", type: .work, isWorkRelated: true)
        let personalProject = projectManager.createProject(name: "Personal", type: .personal, isWorkRelated: false)
        
        // Записываем интервалы
        for _ in 0..<5 {
            statisticsManager.recordCompletedInterval(duration: 1500, projectId: workProject.id)
        }
        
        for _ in 0..<3 {
            statisticsManager.recordCompletedInterval(duration: 1500, projectId: personalProject.id)
        }
        
        let stats = statisticsManager.getWorkPersonalStatisticsCurrentMonth(projectManager: projectManager)
        
        XCTAssertEqual(stats.work, 5)
        XCTAssertEqual(stats.personal, 3)
    }
    
    func testWorkProjectsStatistics() {
        let workProject1 = projectManager.createProject(name: "Work 1", type: .work, isWorkRelated: true)
        let workProject2 = projectManager.createProject(name: "Work 2", type: .creative, isWorkRelated: true)
        let personalProject = projectManager.createProject(name: "Personal", type: .personal, isWorkRelated: false)
        
        // Записываем интервалы
        statisticsManager.recordCompletedInterval(duration: 1500, projectId: workProject1.id)
        statisticsManager.recordCompletedInterval(duration: 1500, projectId: workProject1.id)
        statisticsManager.recordCompletedInterval(duration: 1500, projectId: workProject2.id)
        statisticsManager.recordCompletedInterval(duration: 1500, projectId: personalProject.id) // не должен попасть в статистику
        
        let workStats = statisticsManager.getWorkProjectsStatistics(projectManager: projectManager)
        
        XCTAssertEqual(workStats.count, 2)
        XCTAssertEqual(workStats[workProject1.id], 2)
        XCTAssertEqual(workStats[workProject2.id], 1)
        XCTAssertNil(workStats[personalProject.id])
    }
    
    func testPersonalProjectsStatistics() {
        let workProject = projectManager.createProject(name: "Work", type: .work, isWorkRelated: true)
        let personalProject1 = projectManager.createProject(name: "Personal 1", type: .personal, isWorkRelated: false)
        let personalProject2 = projectManager.createProject(name: "Personal 2", type: .health, isWorkRelated: false)
        
        // Записываем интервалы
        statisticsManager.recordCompletedInterval(duration: 1500, projectId: personalProject1.id)
        statisticsManager.recordCompletedInterval(duration: 1500, projectId: personalProject1.id)
        statisticsManager.recordCompletedInterval(duration: 1500, projectId: personalProject1.id)
        statisticsManager.recordCompletedInterval(duration: 1500, projectId: personalProject2.id)
        statisticsManager.recordCompletedInterval(duration: 1500, projectId: workProject.id) // не должен попасть в статистику
        
        let personalStats = statisticsManager.getPersonalProjectsStatistics(projectManager: projectManager)
        
        XCTAssertEqual(personalStats.count, 2)
        XCTAssertEqual(personalStats[personalProject1.id], 3)
        XCTAssertEqual(personalStats[personalProject2.id], 1)
        XCTAssertNil(personalStats[workProject.id])
    }
    
    func testWorkPersonalStatisticsWithCustomDateRange() {
        let workProject = projectManager.createProject(name: "Work", type: .work, isWorkRelated: true)
        let personalProject = projectManager.createProject(name: "Personal", type: .personal, isWorkRelated: false)
        
        let calendar = Calendar.current
        let now = Date()
        let startDate = calendar.date(byAdding: .day, value: -7, to: now)!
        let endDate = now
        
        // Записываем интервалы
        statisticsManager.recordCompletedInterval(duration: 1500, projectId: workProject.id)
        statisticsManager.recordCompletedInterval(duration: 1500, projectId: personalProject.id)
        
        let stats = statisticsManager.getWorkPersonalStatistics(
            projectManager: projectManager,
            from: startDate,
            to: endDate
        )
        
        XCTAssertEqual(stats.work, 1)
        XCTAssertEqual(stats.personal, 1)
    }
    
    func testMixedProjectTypes() {
        // Создаем проекты разных типов с разными настройками работы/личного времени
        let workCreativeProject = projectManager.createProject(name: "Work Creative", type: .creative, isWorkRelated: true)
        let personalLearningProject = projectManager.createProject(name: "Personal Learning", type: .learning, isWorkRelated: false)
        let workLearningProject = projectManager.createProject(name: "Work Learning", type: .learning, isWorkRelated: true)
        
        // Записываем интервалы
        statisticsManager.recordCompletedInterval(duration: 1500, projectId: workCreativeProject.id)
        statisticsManager.recordCompletedInterval(duration: 1500, projectId: personalLearningProject.id)
        statisticsManager.recordCompletedInterval(duration: 1500, projectId: workLearningProject.id)
        
        let stats = statisticsManager.getWorkPersonalStatisticsToday(projectManager: projectManager)
        
        XCTAssertEqual(stats.work, 2) // workCreativeProject + workLearningProject
        XCTAssertEqual(stats.personal, 1) // personalLearningProject
        
        // Проверяем статистику по рабочим проектам
        let workStats = statisticsManager.getWorkProjectsStatistics(projectManager: projectManager)
        XCTAssertEqual(workStats.count, 2)
        XCTAssertEqual(workStats[workCreativeProject.id], 1)
        XCTAssertEqual(workStats[workLearningProject.id], 1)
        
        // Проверяем статистику по личным проектам
        let personalStats = statisticsManager.getPersonalProjectsStatistics(projectManager: projectManager)
        XCTAssertEqual(personalStats.count, 1)
        XCTAssertEqual(personalStats[personalLearningProject.id], 1)
    }
    
    func testProjectStatisticsWithArchivedProjects() {
        let workProject = projectManager.createProject(name: "Work", type: .work, isWorkRelated: true)
        
        // Записываем интервалы
        statisticsManager.recordCompletedInterval(duration: 1500, projectId: workProject.id)
        
        // Архивируем проект
        projectManager.archiveProject(workProject)
        
        // Статистика должна все еще работать для архивированных проектов
        let stats = statisticsManager.getWorkPersonalStatisticsToday(projectManager: projectManager)
        XCTAssertEqual(stats.work, 1)
        XCTAssertEqual(stats.personal, 0)
        
        let workStats = statisticsManager.getWorkProjectsStatistics(projectManager: projectManager)
        XCTAssertEqual(workStats[workProject.id], 1)
    }
    
    func testEmptyStatistics() {
        // Без интервалов статистика должна быть нулевой
        let stats = statisticsManager.getWorkPersonalStatisticsToday(projectManager: projectManager)
        XCTAssertEqual(stats.work, 0)
        XCTAssertEqual(stats.personal, 0)
        
        let workStats = statisticsManager.getWorkProjectsStatistics(projectManager: projectManager)
        XCTAssertEqual(workStats.count, 0)
        
        let personalStats = statisticsManager.getPersonalProjectsStatistics(projectManager: projectManager)
        XCTAssertEqual(personalStats.count, 0)
    }
}
