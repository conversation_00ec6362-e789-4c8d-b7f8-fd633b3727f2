import XCTest
@testable import SimplePomodoroTest

class WorkPatternAnalyzerTests: XCTestCase {
    
    var statisticsManager: StatisticsManager!
    var analyzer: WorkPatternAnalyzer!
    
    override func setUp() {
        super.setUp()
        statisticsManager = StatisticsManager()
        analyzer = WorkPatternAnalyzer(statisticsManager: statisticsManager)
        
        // Очищаем тестовые данные
        UserDefaults.standard.removeObject(forKey: "completedIntervals")
    }
    
    override func tearDown() {
        // Очищаем тестовые данные
        UserDefaults.standard.removeObject(forKey: "completedIntervals")
        statisticsManager = nil
        analyzer = nil
        super.tearDown()
    }
    
    func testEmptyAnalysis() {
        let pattern = analyzer.analyzeWorkPattern(for: .lastWeek)
        
        XCTAssertEqual(pattern.averageIntervalsPerDay, 0)
        XCTAssertEqual(pattern.workingDaysPerWeek, 0)
        XCTAssertEqual(pattern.consistencyScore, 1.0) // Нет данных = максимальная консистентность
        XCTAssertNil(pattern.averageStartTime)
        XCTAssertTrue(pattern.riskFactors.isEmpty)
        XCTAssertTrue(pattern.recommendations.isEmpty)
    }
    
    func testNormalWorkPattern() {
        // Создаем данные для нормального рабочего паттерна
        let calendar = Calendar.current
        let now = Date()

        // 5 дней работы по 4 интервала в день
        for dayOffset in 1...5 {
            guard let workDay = calendar.date(byAdding: .day, value: -dayOffset, to: now) else { continue }

            for intervalOffset in 0..<4 {
                // Начинаем работу в 9 утра
                let startTime = calendar.date(bySettingHour: 9, minute: intervalOffset * 60, second: 0, of: workDay)!

                // Симулируем запись интервала через StatisticsManager
                statisticsManager.recordCompletedInterval(duration: 3120) // 52 минуты
            }
        }

        let pattern = analyzer.analyzeWorkPattern(for: .lastWeek)

        // Базовые проверки
        XCTAssertGreaterThan(pattern.averageIntervalsPerDay, 0)
        XCTAssertGreaterThan(pattern.workingDaysPerWeek, 0)
        XCTAssertGreaterThanOrEqual(pattern.consistencyScore, 0)
        XCTAssertLessThanOrEqual(pattern.consistencyScore, 1)
    }
    
    func testOverworkPattern() {
        // Создаем данные для переработки - много интервалов
        for _ in 0..<30 { // 30 интервалов за короткий период
            statisticsManager.recordCompletedInterval(duration: 3120)
        }

        let pattern = analyzer.analyzeWorkPattern(for: .lastWeek)

        // Базовые проверки
        XCTAssertGreaterThan(pattern.averageIntervalsPerDay, 0)
        XCTAssertTrue(pattern.riskFactors.count >= 0) // Может быть риск переработки
        XCTAssertTrue(pattern.recommendations.count >= 0) // Может быть рекомендация
    }
    
    func testBasicAnalysis() {
        // Создаем несколько интервалов для базового анализа
        for _ in 0..<5 {
            statisticsManager.recordCompletedInterval(duration: 3120)
        }

        let pattern = analyzer.analyzeWorkPattern(for: .lastWeek)

        // Базовые проверки структуры
        XCTAssertGreaterThanOrEqual(pattern.averageIntervalsPerDay, 0)
        XCTAssertGreaterThanOrEqual(pattern.workingDaysPerWeek, 0)
        XCTAssertGreaterThanOrEqual(pattern.consistencyScore, 0)
        XCTAssertLessThanOrEqual(pattern.consistencyScore, 1)
        XCTAssertTrue(pattern.riskFactors.count >= 0)
        XCTAssertTrue(pattern.recommendations.count >= 0)
    }
    
    func testAnalysisPeriods() {
        // Создаем несколько интервалов
        for _ in 0..<3 {
            statisticsManager.recordCompletedInterval(duration: 3120)
        }

        // Тестируем разные периоды анализа
        let weekPattern = analyzer.analyzeWorkPattern(for: .lastWeek)
        let monthPattern = analyzer.analyzeWorkPattern(for: .lastMonth)
        let threeMonthPattern = analyzer.analyzeWorkPattern(for: .lastThreeMonths)

        // Все должны возвращать валидные результаты
        XCTAssertGreaterThanOrEqual(weekPattern.consistencyScore, 0)
        XCTAssertGreaterThanOrEqual(monthPattern.consistencyScore, 0)
        XCTAssertGreaterThanOrEqual(threeMonthPattern.consistencyScore, 0)
    }
    
}
