import XCTest
@testable import SimplePomodoroTest

class PomodoroTimerTests: XCTestCase {
    
    var timer: PomodoroTimer!
    
    override func setUp() {
        super.setUp()
        timer = PomodoroTimer()
    }
    
    override func tearDown() {
        timer = nil
        super.tearDown()
    }
    
    func testInitialState() {
        XCTAssertEqual(timer.state, .idle)
        XCTAssertEqual(timer.timeRemaining, PomodoroTimer.workDuration)
        XCTAssertEqual(timer.overtimeElapsed, 0)
        XCTAssertEqual(timer.reminderCount, 0)
        XCTAssertFalse(timer.isActive())
    }
    
    func testStartInterval() {
        timer.startInterval()
        
        XCTAssertEqual(timer.state, .working)
        XCTAssertTrue(timer.isActive())
        XCTAssertEqual(timer.timeRemaining, PomodoroTimer.workDuration)
    }
    
    func testStopInterval() {
        timer.startInterval()
        timer.stopInterval()
        
        XCTAssertEqual(timer.state, .idle)
        XCTAssertFalse(timer.isActive())
        XCTAssertEqual(timer.timeRemaining, PomodoroTimer.workDuration)
        XCTAssertEqual(timer.overtimeElapsed, 0)
    }
    
    func testCompleteInterval() {
        timer.startInterval()
        timer.completeInterval()
        
        XCTAssertEqual(timer.state, .idle)
        XCTAssertFalse(timer.isActive())
        XCTAssertEqual(timer.timeRemaining, PomodoroTimer.workDuration)
        XCTAssertEqual(timer.overtimeElapsed, 0)
    }
    
    func testFormatTime() {
        XCTAssertEqual(timer.formatTime(0), "00:00")
        XCTAssertEqual(timer.formatTime(60), "01:00")
        XCTAssertEqual(timer.formatTime(125), "02:05")
        XCTAssertEqual(timer.formatTime(3661), "61:01")
    }
    
    func testGetStatusText() {
        // Idle state
        XCTAssertEqual(timer.getStatusText(), "🍅")
        
        // Working state
        timer.startInterval()
        let workingStatus = timer.getStatusText()
        XCTAssertTrue(workingStatus.hasPrefix("🍅"))
        XCTAssertTrue(workingStatus.contains(":"))
        
        // Overtime state (симулируем)
        timer.stopInterval()
        timer = PomodoroTimer()
        timer.state = .overtime
        timer.overtimeElapsed = 300 // 5 минут
        let overtimeStatus = timer.getStatusText()
        XCTAssertTrue(overtimeStatus.hasPrefix("⚠️"))
        XCTAssertTrue(overtimeStatus.contains("05:00"))
    }
    
    func testCallbacks() {
        var stateChangedCalled = false
        var timeUpdateCalled = false
        var intervalCompletedCalled = false
        var reminderTriggeredCalled = false
        
        timer.onStateChanged = { _ in stateChangedCalled = true }
        timer.onTimeUpdate = { _, _ in timeUpdateCalled = true }
        timer.onIntervalCompleted = { intervalCompletedCalled = true }
        timer.onReminderTriggered = { _ in reminderTriggeredCalled = true }
        
        timer.startInterval()
        
        XCTAssertTrue(stateChangedCalled)
        
        // Симулируем обновление времени
        timer.onTimeUpdate?(timer.timeRemaining, timer.overtimeElapsed)
        XCTAssertTrue(timeUpdateCalled)
    }
    
    func testMultipleStartCalls() {
        timer.startInterval()
        let firstState = timer.state
        let firstTime = timer.timeRemaining
        
        // Повторный вызов не должен изменить состояние
        timer.startInterval()
        
        XCTAssertEqual(timer.state, firstState)
        XCTAssertEqual(timer.timeRemaining, firstTime)
    }
    
    func testStopWhenIdle() {
        // Остановка в состоянии idle не должна вызывать ошибок
        timer.stopInterval()

        XCTAssertEqual(timer.state, .idle)
        XCTAssertFalse(timer.isActive())
    }

    func testFullIntervalTracking() {
        var fullIntervalCompleted = false
        var completedDuration: TimeInterval = 0

        timer.onFullIntervalCompleted = { duration in
            fullIntervalCompleted = true
            completedDuration = duration
        }

        // Запускаем полноценный интервал (не тестовый)
        timer.startInterval()

        // Симулируем естественное завершение - устанавливаем значения после startInterval
        timer.setNaturallyCompleted(true)
        // isFullInterval уже установлен в true в startInterval для полноценных интервалов
        // intervalStartTime уже установлен в startInterval

        timer.completeInterval()

        // Проверяем, что колбэк был вызван
        XCTAssertTrue(fullIntervalCompleted)
        XCTAssertGreaterThan(completedDuration, 0)
    }

    func testTestIntervalNotTracked() {
        var fullIntervalCompleted = false

        timer.onFullIntervalCompleted = { _ in
            fullIntervalCompleted = true
        }

        // Запускаем тестовый интервал
        timer.startInterval(testDuration: 3)

        // Симулируем завершение
        timer.state = .overtime
        timer.completeInterval()

        // Проверяем, что колбэк НЕ был вызван для тестового интервала
        XCTAssertFalse(fullIntervalCompleted)
    }

    func testManualStopNotTracked() {
        var fullIntervalCompleted = false

        timer.onFullIntervalCompleted = { _ in
            fullIntervalCompleted = true
        }

        // Запускаем полноценный интервал
        timer.startInterval()

        // Останавливаем вручную (не естественное завершение)
        timer.stopInterval()

        // Проверяем, что колбэк НЕ был вызван
        XCTAssertFalse(fullIntervalCompleted)
    }
}
