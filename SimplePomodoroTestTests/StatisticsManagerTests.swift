import XCTest
@testable import SimplePomodoroTest

class StatisticsManagerTests: XCTestCase {
    
    var statisticsManager: StatisticsManager!
    let testKey = "testCompletedIntervals"
    
    override func setUp() {
        super.setUp()
        statisticsManager = StatisticsManager()
        
        // Очищаем тестовые данные
        UserDefaults.standard.removeObject(forKey: "completedIntervals")
    }
    
    override func tearDown() {
        // Очищаем тестовые данные
        UserDefaults.standard.removeObject(forKey: "completedIntervals")
        statisticsManager = nil
        super.tearDown()
    }
    
    func testRecordCompletedInterval() {
        // Записываем интервал
        statisticsManager.recordCompletedInterval(duration: 3120) // 52 минуты
        
        // Проверяем, что интервал записался
        let total = statisticsManager.getTotalCompletedIntervals()
        XCTAssertEqual(total, 1)
    }
    
    func testMultipleIntervals() {
        // Записываем несколько интервалов
        statisticsManager.recordCompletedInterval(duration: 3120)
        statisticsManager.recordCompletedInterval(duration: 3120)
        statisticsManager.recordCompletedInterval(duration: 3120)
        
        let total = statisticsManager.getTotalCompletedIntervals()
        XCTAssertEqual(total, 3)
    }
    
    func testStatsForToday() {
        // Записываем интервал сегодня
        statisticsManager.recordCompletedInterval(duration: 3120)
        
        let todayStats = statisticsManager.getStatsForToday()
        XCTAssertEqual(todayStats, 1)
        
        // Вчера должно быть 0
        let yesterdayStats = statisticsManager.getStatsForYesterday()
        XCTAssertEqual(yesterdayStats, 0)
    }
    
    func testStatsForCurrentWeek() {
        // Записываем интервал
        statisticsManager.recordCompletedInterval(duration: 3120)
        
        let weekStats = statisticsManager.getStatsForCurrentWeek()
        XCTAssertGreaterThanOrEqual(weekStats, 1)
    }
    
    func testStatsForCurrentMonth() {
        // Записываем интервал
        statisticsManager.recordCompletedInterval(duration: 3120)
        
        let monthStats = statisticsManager.getStatsForCurrentMonth()
        XCTAssertGreaterThanOrEqual(monthStats, 1)
    }
    
    func testRecentIntervals() {
        // Записываем несколько интервалов
        for i in 1...5 {
            statisticsManager.recordCompletedInterval(duration: TimeInterval(3120 + i))
        }

        let recentIntervals = statisticsManager.getRecentIntervals(limit: 3)
        XCTAssertEqual(recentIntervals.count, 3)

        // Проверяем, что последний интервал первый в списке (reversed)
        // getRecentIntervals возвращает последние интервалы в обратном порядке
        XCTAssertEqual(recentIntervals[0].1, 3125) // 3120 + 5 (последний записанный)
        XCTAssertEqual(recentIntervals[1].1, 3124) // 3120 + 4
        XCTAssertEqual(recentIntervals[2].1, 3123) // 3120 + 3
    }
    
    func testEmptyStatistics() {
        // Проверяем пустую статистику
        XCTAssertEqual(statisticsManager.getTotalCompletedIntervals(), 0)
        XCTAssertEqual(statisticsManager.getStatsForToday(), 0)
        XCTAssertEqual(statisticsManager.getStatsForYesterday(), 0)
        XCTAssertEqual(statisticsManager.getStatsForCurrentWeek(), 0)
        XCTAssertEqual(statisticsManager.getStatsForCurrentMonth(), 0)
        XCTAssertEqual(statisticsManager.getRecentIntervals().count, 0)
    }
    
    func testDateRangeFiltering() {
        let calendar = Calendar.current
        let now = Date()
        
        // Создаем даты для тестирования
        let today = calendar.startOfDay(for: now)
        let tomorrow = calendar.date(byAdding: .day, value: 1, to: today)!
        
        // Записываем интервал
        statisticsManager.recordCompletedInterval(duration: 3120)
        
        // Проверяем фильтрацию по диапазону дат
        let todayCount = statisticsManager.getCompletedIntervalsForDateRange(from: today, to: tomorrow)
        XCTAssertEqual(todayCount, 1)
        
        // Проверяем диапазон, который не включает сегодня
        let yesterday = calendar.date(byAdding: .day, value: -1, to: today)!
        let yesterdayCount = statisticsManager.getCompletedIntervalsForDateRange(from: yesterday, to: today)
        XCTAssertEqual(yesterdayCount, 0)
    }
}
