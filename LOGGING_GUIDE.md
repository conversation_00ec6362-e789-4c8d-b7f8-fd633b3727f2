# 📋 Руководство по системе логирования uProd

## 🚀 Что добавлено

В приложение uProd добавлена комплексная система логирования для диагностики крашей и мониторинга работы приложения.

### ✨ Основные возможности

- **Красивые и компактные логи** с эмодзи и временными метками
- **Автоматическая ротация логов** (максимум 3 файла по 2MB)
- **Обработка крашей** с детальной диагностикой
- **Мониторинг памяти и производительности**
- **Встроенный анализатор логов** в меню приложения

## 📁 Где находятся логи

Логи сохраняются в:
```
~/Library/Application Support/uProd/Logs/
```

Формат файлов: `uProd_YYYY-MM-DD_HH-mm-ss.log`

## 🔍 Как просматривать логи

### Через приложение
1. Кликните на иконку uProd в menu bar
2. Выберите "📋 Анализ логов" - покажет окно с анализом
3. Выберите "📁 Открыть папку логов" - откроет Finder

### Через терминал
Используйте скрипт `view_logs.sh`:
```bash
./view_logs.sh
```

Или напрямую:
```bash
# Просмотр последних записей
tail -f ~/Library/Application\ Support/uProd/Logs/uProd_*.log

# Поиск ошибок
grep -E "\[ERR\]|\[WRN\]|\[CRT\]" ~/Library/Application\ Support/uProd/Logs/uProd_*.log
```

## 📊 Формат логов

Каждая запись имеет формат:
```
HH:mm:ss [LVL] Category 🔥 Сообщение
```

Где:
- **HH:mm:ss** - время
- **[LVL]** - уровень (DBG/INF/WRN/ERR/CRT)
- **Category** - категория (App/Timer/UI/Memory/etc.)
- **🔥** - эмодзи для быстрой визуальной идентификации

### Примеры записей:
```
18:45:23 [INF] Main     🎯 uProd запускается
18:45:24 [INF] App      🚀 Запуск приложения
18:45:24 [INF] Setup    📊 Настройка статистики
18:45:25 [INF] Timer    ▶️ Интервал запущен (3120с, интервалов: 0)
18:45:26 [DBG] Memory   💾 45.2MB
```

## 🚨 Диагностика крашей

При краше приложения система автоматически:

1. **Логирует детали краша** с stack trace
2. **Сохраняет состояние приложения** (таймеры, окна, память)
3. **Показывает диалог пользователю** с информацией
4. **Сохраняет финальное состояние** перед завершением

### Что искать в логах при краше:
- Записи с `[CRT]` (критические ошибки)
- Записи с `💥` (исключения)
- Записи с `⚡` (системные сигналы)
- Последние записи перед остановкой логирования

## 📈 Мониторинг производительности

Система автоматически отслеживает:

- **Память**: каждые 5 минут
- **Состояние приложения**: каждые 10 минут
- **CPU и потоки**: каждые 5 минут
- **Обновления UI**: каждое 10-е обновление

## 🔧 Настройки логирования

Основные параметры в `Logger.swift`:
- `maxLogFileSize = 2MB` - размер файла
- `maxLogFiles = 3` - количество файлов
- Интервалы мониторинга можно изменить в `CrashHandler.swift`

## 🎯 Как воспроизвести и диагностировать краш

1. **Запустите приложение** с новой системой логирования
2. **Используйте как обычно** в течение 30+ минут
3. **Если произойдет краш**:
   - Откройте "📋 Анализ логов" в меню
   - Или используйте `./view_logs.sh` → "3) Поиск ошибок"
   - Найдите последние записи перед крашем
4. **Отправьте логи** для анализа

## 🛠️ Полезные команды

```bash
# Мониторинг в реальном времени
tail -f ~/Library/Application\ Support/uProd/Logs/uProd_*.log

# Только ошибки и предупреждения
grep -E "\[ERR\]|\[WRN\]|\[CRT\]" ~/Library/Application\ Support/uProd/Logs/uProd_*.log

# Статистика логов
wc -l ~/Library/Application\ Support/uProd/Logs/uProd_*.log
```

## 🎉 Готово!

Теперь при любых проблемах с приложением у нас будут детальные логи для диагностики. Система работает автоматически и не требует дополнительной настройки.

**Важно**: Логи содержат только техническую информацию о работе приложения и не включают личные данные.
