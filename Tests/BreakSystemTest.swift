#!/usr/bin/env swift

//
//  BreakSystemTest.swift
//  uProd Tests
//
//  Юнит-тесты для системы отдыха и качества перерывов
//

import Foundation

// MARK: - Test Framework (общий для всех тестов)

struct TestResult {
    let name: String
    let passed: Bool
    let message: String
    let duration: TimeInterval
}

class TestRunner {
    private var results: [TestResult] = []
    
    func test(_ name: String, _ testFunc: () throws -> Void) {
        let startTime = Date()
        do {
            try testFunc()
            let duration = Date().timeIntervalSince(startTime)
            results.append(TestResult(name: name, passed: true, message: "✅ ПРОШЕЛ", duration: duration))
            print("✅ \(name)")
        } catch {
            let duration = Date().timeIntervalSince(startTime)
            results.append(TestResult(name: name, passed: false, message: "❌ ПРОВАЛЕН: \(error)", duration: duration))
            print("❌ \(name): \(error)")
        }
    }
    
    func assert(_ condition: Bool, _ message: String) throws {
        if !condition {
            throw TestError.assertionFailed(message)
        }
    }
    
    func assertEqual<T: Equatable>(_ actual: T, _ expected: T, _ message: String = "") throws {
        if actual != expected {
            throw TestError.assertionFailed("Ожидалось: \(expected), получено: \(actual). \(message)")
        }
    }
    
    func printSummary() -> Bool {
        let passed = results.filter { $0.passed }.count
        let total = results.count
        let totalTime = results.reduce(0) { $0 + $1.duration }
        
        print("\n" + String(repeating: "=", count: 60))
        print("☕ ТЕСТЫ СИСТЕМЫ ОТДЫХА")
        print(String(repeating: "=", count: 60))
        print("📊 Всего тестов: \(total)")
        print("✅ Прошло: \(passed)")
        print("❌ Провалено: \(total - passed)")
        print("⏱️ Время выполнения: \(String(format: "%.3f", totalTime))с")
        print("📈 Успешность: \(Int((Double(passed) / Double(total)) * 100))%")
        print(String(repeating: "=", count: 60))
        
        if passed == total {
            print("🎉 ВСЕ ТЕСТЫ ПРОШЛИ! Система отдыха работает корректно.")
            return true
        } else {
            print("⚠️ ЕСТЬ ПРОБЛЕМЫ! Требуется исправление.")
            return false
        }
    }
}

enum TestError: Error {
    case assertionFailed(String)
}

// MARK: - Simplified BreakQualityTracker for Testing

class TestableBreakQualityTracker {
    private var isTracking = false
    private var activityDuringBreak: [Bool] = []
    private var breakStartTime: Date?
    
    func startTracking() {
        isTracking = true
        activityDuringBreak.removeAll()
        breakStartTime = Date()
    }
    
    func recordActivity(isActive: Bool) {
        if isTracking {
            activityDuringBreak.append(isActive)
        }
    }
    
    func stopTracking() -> Int {
        guard isTracking else { return 0 }
        
        isTracking = false
        
        if activityDuringBreak.isEmpty {
            return 100 // Идеальный отдых
        }
        
        let activeCount = activityDuringBreak.filter { $0 }.count
        let totalCount = activityDuringBreak.count
        let activityPercentage = (Double(activeCount) / Double(totalCount)) * 100
        
        // Качество отдыха = 100% - процент активности
        return max(0, 100 - Int(activityPercentage))
    }
    
    func getBreakDuration() -> TimeInterval {
        guard let startTime = breakStartTime else { return 0 }
        return Date().timeIntervalSince(startTime)
    }
    
    func isCurrentlyTracking() -> Bool {
        return isTracking
    }
}

// MARK: - Tests

func runBreakSystemTests() {
    let runner = TestRunner()
    
    // Тест 1: Начало отслеживания отдыха
    runner.test("Начало отслеживания отдыха") {
        let tracker = TestableBreakQualityTracker()
        
        tracker.startTracking()
        
        try runner.assert(tracker.isCurrentlyTracking(), "Отслеживание должно быть активно")
    }
    
    // Тест 2: Идеальный отдых (без активности)
    runner.test("Идеальный отдых (без активности)") {
        let tracker = TestableBreakQualityTracker()
        
        tracker.startTracking()
        // Не записываем никакой активности
        let quality = tracker.stopTracking()
        
        try runner.assertEqual(quality, 100, "Качество отдыха должно быть 100%")
    }
    
    // Тест 3: Плохой отдых (постоянная активность)
    runner.test("Плохой отдых (постоянная активность)") {
        let tracker = TestableBreakQualityTracker()
        
        tracker.startTracking()
        // Записываем только активность
        for _ in 1...10 {
            tracker.recordActivity(isActive: true)
        }
        let quality = tracker.stopTracking()
        
        try runner.assertEqual(quality, 0, "Качество отдыха должно быть 0%")
    }
    
    // Тест 4: Средний отдых (50% активности)
    runner.test("Средний отдых (50% активности)") {
        let tracker = TestableBreakQualityTracker()
        
        tracker.startTracking()
        // 50% активности, 50% покоя
        for _ in 1...5 {
            tracker.recordActivity(isActive: true)
            tracker.recordActivity(isActive: false)
        }
        let quality = tracker.stopTracking()
        
        try runner.assertEqual(quality, 50, "Качество отдыха должно быть 50%")
    }
    
    // Тест 5: Остановка отслеживания
    runner.test("Остановка отслеживания") {
        let tracker = TestableBreakQualityTracker()
        
        tracker.startTracking()
        tracker.stopTracking()
        
        try runner.assert(!tracker.isCurrentlyTracking(), "Отслеживание должно быть остановлено")
    }
    
    // Тест 6: Активность не записывается когда отслеживание выключено
    runner.test("Активность не записывается без отслеживания") {
        let tracker = TestableBreakQualityTracker()
        
        // Записываем активность БЕЗ включения отслеживания
        tracker.recordActivity(isActive: true)
        tracker.startTracking()
        let quality = tracker.stopTracking()
        
        try runner.assertEqual(quality, 100, "Активность до начала отслеживания не должна учитываться")
    }
    
    let success = runner.printSummary()
    exit(success ? 0 : 1)
}

// MARK: - Main

print("🚀 Запуск тестов системы отдыха...")
print("📅 \(Date())")
print("")

runBreakSystemTests()
