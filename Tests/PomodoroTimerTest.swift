#!/usr/bin/env swift

//
//  PomodoroTimerTest.swift
//  uProd Tests
//
//  Юнит-тесты для системы формальных интервалов (Pomodoro Timer)
//

import Foundation

// MARK: - Test Framework (общий для всех тестов)

struct TestResult {
    let name: String
    let passed: Bool
    let message: String
    let duration: TimeInterval
}

class TestRunner {
    private var results: [TestResult] = []
    
    func test(_ name: String, _ testFunc: () throws -> Void) {
        let startTime = Date()
        do {
            try testFunc()
            let duration = Date().timeIntervalSince(startTime)
            results.append(TestResult(name: name, passed: true, message: "✅ ПРОШЕЛ", duration: duration))
            print("✅ \(name)")
        } catch {
            let duration = Date().timeIntervalSince(startTime)
            results.append(TestResult(name: name, passed: false, message: "❌ ПРОВАЛЕН: \(error)", duration: duration))
            print("❌ \(name): \(error)")
        }
    }
    
    func assert(_ condition: Bool, _ message: String) throws {
        if !condition {
            throw TestError.assertionFailed(message)
        }
    }
    
    func assertEqual<T: Equatable>(_ actual: T, _ expected: T, _ message: String = "") throws {
        if actual != expected {
            throw TestError.assertionFailed("Ожидалось: \(expected), получено: \(actual). \(message)")
        }
    }
    
    func printSummary() -> Bool {
        let passed = results.filter { $0.passed }.count
        let total = results.count
        let totalTime = results.reduce(0) { $0 + $1.duration }
        
        print("\n" + String(repeating: "=", count: 60))
        print("⏰ ТЕСТЫ ФОРМАЛЬНЫХ ИНТЕРВАЛОВ")
        print(String(repeating: "=", count: 60))
        print("📊 Всего тестов: \(total)")
        print("✅ Прошло: \(passed)")
        print("❌ Провалено: \(total - passed)")
        print("⏱️ Время выполнения: \(String(format: "%.3f", totalTime))с")
        print("📈 Успешность: \(Int((Double(passed) / Double(total)) * 100))%")
        print(String(repeating: "=", count: 60))
        
        if passed == total {
            print("🎉 ВСЕ ТЕСТЫ ПРОШЛИ! Формальные интервалы работают корректно.")
            return true
        } else {
            print("⚠️ ЕСТЬ ПРОБЛЕМЫ! Требуется исправление.")
            return false
        }
    }
}

enum TestError: Error {
    case assertionFailed(String)
}

// MARK: - Simplified PomodoroTimer for Testing

enum TestPomodoroState {
    case idle
    case working
    case shortBreak
    case longBreak
    case paused
}

class TestablePomodoroTimer {
    var state: TestPomodoroState = .idle
    var currentInterval: Int = 0
    var workDuration: TimeInterval = 25 * 60 // 25 минут
    var shortBreakDuration: TimeInterval = 5 * 60 // 5 минут
    var longBreakDuration: TimeInterval = 15 * 60 // 15 минут
    
    func startWork() {
        state = .working
        currentInterval += 1
    }
    
    func startShortBreak() {
        state = .shortBreak
    }
    
    func startLongBreak() {
        state = .longBreak
    }
    
    func pause() {
        if state == .working {
            state = .paused
        }
    }
    
    func resume() {
        if state == .paused {
            state = .working
        }
    }
    
    func stop() {
        state = .idle
        currentInterval = 0
    }
    
    func isWorking() -> Bool {
        return state == .working
    }
    
    func isOnBreak() -> Bool {
        return state == .shortBreak || state == .longBreak
    }
    
    func shouldTakeLongBreak() -> Bool {
        return currentInterval % 4 == 0 && currentInterval > 0
    }
}

// MARK: - Tests

func runPomodoroTimerTests() {
    let runner = TestRunner()
    
    // Тест 1: Запуск рабочего интервала
    runner.test("Запуск рабочего интервала") {
        let timer = TestablePomodoroTimer()
        
        timer.startWork()
        
        try runner.assert(timer.isWorking(), "Таймер должен быть в состоянии работы")
        try runner.assertEqual(timer.currentInterval, 1, "Должен быть первый интервал")
    }
    
    // Тест 2: Переход на короткий перерыв
    runner.test("Переход на короткий перерыв") {
        let timer = TestablePomodoroTimer()
        
        timer.startWork()
        timer.startShortBreak()
        
        try runner.assert(timer.isOnBreak(), "Таймер должен быть на перерыве")
        try runner.assertEqual(timer.state, .shortBreak, "Должен быть короткий перерыв")
    }
    
    // Тест 3: Логика длинного перерыва
    runner.test("Логика длинного перерыва (каждый 4-й интервал)") {
        let timer = TestablePomodoroTimer()
        
        // Симулируем 4 интервала
        for _ in 1...4 {
            timer.startWork()
        }
        
        try runner.assert(timer.shouldTakeLongBreak(), "После 4 интервалов должен быть длинный перерыв")
        try runner.assertEqual(timer.currentInterval, 4, "Должно быть 4 интервала")
    }
    
    // Тест 4: Пауза и возобновление
    runner.test("Пауза и возобновление работы") {
        let timer = TestablePomodoroTimer()
        
        timer.startWork()
        timer.pause()
        try runner.assertEqual(timer.state, .paused, "Таймер должен быть на паузе")
        
        timer.resume()
        try runner.assert(timer.isWorking(), "Таймер должен возобновить работу")
    }
    
    // Тест 5: Остановка таймера
    runner.test("Остановка таймера") {
        let timer = TestablePomodoroTimer()
        
        timer.startWork()
        timer.startWork() // Второй интервал
        timer.stop()
        
        try runner.assertEqual(timer.state, .idle, "Таймер должен быть в состоянии покоя")
        try runner.assertEqual(timer.currentInterval, 0, "Счетчик интервалов должен сброситься")
    }
    
    let success = runner.printSummary()
    exit(success ? 0 : 1)
}

// MARK: - Main

print("🚀 Запуск тестов формальных интервалов...")
print("📅 \(Date())")
print("")

runPomodoroTimerTests()
