# uProd

Приложение для повышения продуктивности с Pomodoro таймером для macOS, разработанное для предотвращения переработок и поддержания продуктивности.

## Особенности

- **52-минутные рабочие интервалы** - оптимальная продолжительность для концентрации
- **Предотвращение переработок** - система напоминаний каждые 5 минут после завершения интервала
- **Полноэкранные уведомления** - невозможно игнорировать напоминания об отдыхе
- **Усиливающиеся сообщения** - чем дольше переработка, тем настойчивее напоминания
- **Минималистичный интерфейс** - работает из системного трея

## Как использовать

### Настройка длительности интервала
1. Кликните на иконку помидора 🍅 в системном трее
2. Выберите "Настройки..." (⌘,)
3. Установите желаемую длительность в минутах
4. Нажмите "Сохранить"

**Для тестирования:** можно установить 1 минуту, чтобы быстро проверить работу полноэкранных уведомлений.

### Запуск интервала
1. Кликните на иконку помидора 🍅 в системном трее
2. Выберите "Начать интервал (X мин)" где X - установленная длительность
3. Таймер начнет обратный отсчет, отображаемый в трее

### Во время работы
- В трее отображается оставшееся время: `🍅 51:23`
- Можно досрочно завершить интервал через меню

### После завершения интервала
1. Появится полноэкранное окно с предложением отдохнуть
2. Выберите:
   - **"Завершить интервал"** - переход к отдыху
   - **"Поработать еще 5 минут"** - продолжение работы

### Система напоминаний при переработке
- Каждые 5 минут появляется напоминание об отдыхе
- Сообщения становятся все более настойчивыми:
  - 1-е напоминание: "Время для отдыха!"
  - 2-е напоминание: "Пожалуйста, сделайте перерыв"
  - 3-е напоминание: "Серьезно, пора отдохнуть!"
  - 4-е напоминание: "⚠️ ВНИМАНИЕ! Продолжение работы приведет к выгоранию!"
  - 5-е напоминание: "🚨 КРИТИЧНО! Немедленно прекратите работу!"
  - Далее: "🔥 ОПАСНОСТЬ ВЫГОРАНИЯ!"

### Отображение переработки
- В трее показывается время переработки: `⚠️ +15:30`
- Время обновляется каждую секунду

## Технические детали

### Архитектура
- **PomodoroTimer** - основная логика таймера
- **IntervalCompletionWindow** - полноэкранное окно уведомлений
- **AppDelegate** - управление статус баром и интеграция компонентов

### Состояния таймера
- `idle` - ожидание запуска
- `working` - активный рабочий интервал
- `overtime` - переработка после завершения интервала

### Константы
- Рабочий интервал: 52 минуты (3120 секунд)
- Интервал напоминаний: 5 минут (300 секунд)

## Сборка и запуск

### Требования
- macOS 15.5+
- Xcode 16+
- Swift 5+

### Команды
```bash
# Сборка
xcodebuild -project SimplePomodoroTest.xcodeproj -scheme SimplePomodoroTest -configuration Debug build

# Запуск тестов
xcodebuild test -project SimplePomodoroTest.xcodeproj -scheme SimplePomodoroTest -destination 'platform=macOS'

# Запуск приложения
open /Users/<USER>/Library/Developer/Xcode/DerivedData/SimplePomodoroTest-*/Build/Products/Debug/SimplePomodoroTest.app
```

## Тестирование

Проект включает комплексные unit-тесты для проверки:
- Инициализации таймера
- Запуска и остановки интервалов
- Форматирования времени
- Отображения статуса
- Обработки колбэков
- Граничных случаев

Запуск тестов: `xcodebuild test -project SimplePomodoroTest.xcodeproj -scheme SimplePomodoroTest`

## Философия

Это приложение создано для борьбы с переработками - одной из главных причин профессионального выгорания. Вместо простых уведомлений, которые легко игнорировать, используется система прогрессивных напоминаний, которые становятся все более настойчивыми.

Цель - не просто отследить время работы, а активно защитить пользователя от вредных привычек переработки.

## Лицензия

MIT License - свободное использование и модификация.
