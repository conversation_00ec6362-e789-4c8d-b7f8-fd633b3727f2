# Таймер отдыха - Руководство по использованию

## Обзор функциональности

Новая функция таймера отдыха автоматически запускается после завершения рабочего интервала (нажатия кнопки "Take a break"). Система отслеживает качество отдыха и сохраняет статистику для анализа.

## Основные возможности

### 1. Автоматический запуск отдыха
- После нажатия "Take a break" автоматически запускается 15-минутный таймер отдыха
- Показывается окно с напоминанием закрыть ноутбук и отдохнуть
- Таймер продолжает работать даже при закрытии ноутбука

### 2. Отслеживание активности
- Система мониторит активность пользователя во время отдыха
- Отслеживается движение мыши, нажатия клавиш, клики и скроллинг
- Проверяется состояние системы (спящий режим, активные процессы)
- Данные об активности сохраняются в статистику

### 3. Окна уведомлений

#### Окно начала отдыха (BreakStartWindow)
- Показывает таймер обратного отсчета
- Напоминает закрыть ноутбук
- Предупреждает об обнаруженной активности
- Кнопки: "Понятно" (продолжить отдых) и "Пропустить" (отменить отдых)

#### Окно завершения отдыха (BreakEndWindow)
- Показывается по окончании таймера отдыха
- Отображает статистику отдыха (качество, активность)
- Предлагает начать новый интервал
- Кнопки: "Начать интервал", "Выбрать проект", "Позже"

### 4. Статистика отдыха
- Количество отдыхов за период
- Качество отдыха (процент отдыхов без активности компьютера)
- Время активности компьютера во время отдыха
- Интеграция с общей статистикой приложения

## Техническая архитектура

### Основные компоненты

1. **BreakTimer** - Основной класс управления таймером отдыха
   - Отслеживание времени отдыха
   - Мониторинг активности пользователя
   - Генерация статистики

2. **PomodoroState.onBreak** - Новое состояние в основном таймере
   - Интеграция с существующими состояниями
   - Переходы между состояниями

3. **BreakStartWindow** - Окно начала отдыха
   - Таймер обратного отсчета
   - Предупреждения об активности
   - Управление отдыхом

4. **BreakEndWindow** - Окно завершения отдыха
   - Статистика отдыха
   - Варианты продолжения работы

5. **StatisticsManager** - Расширенная статистика
   - Сохранение данных об отдыхе
   - Анализ качества отдыха
   - Интеграция с существующей статистикой

### Структуры данных

```swift
struct BreakStatistics {
    let startTime: Date
    let duration: TimeInterval
    let elapsed: TimeInterval
    let progress: Double
    let wasComputerActive: Bool
    let computerActiveTime: TimeInterval
}

struct CompletedBreak: Codable {
    let date: Date
    let duration: TimeInterval
    let wasComputerActive: Bool
    let computerActiveTime: TimeInterval
}
```

## Использование API

### Запуск отдыха
```swift
pomodoroTimer.startBreak()
```

### Остановка отдыха
```swift
pomodoroTimer.stopBreak()
```

### Получение статистики
```swift
let stats = pomodoroTimer.getBreakStatistics()
let quality = statisticsManager.getBreakQualityForDateRange(from: startDate, to: endDate)
```

### Колбэки
```swift
pomodoroTimer.onBreakStarted = { 
    // Отдых начался
}

pomodoroTimer.onBreakCompleted = { 
    // Отдых завершен
}

pomodoroTimer.onBreakActivityDetected = { 
    // Обнаружена активность во время отдыха
}
```

## Настройки и конфигурация

### Продолжительность отдыха
По умолчанию: 15 минут (900 секунд)
```swift
BreakTimer.defaultBreakDuration = 15 * 60
```

### Порог активности
По умолчанию: 30 секунд бездействия
```swift
let activityThreshold: TimeInterval = 30.0
```

### Интервал проверки активности
По умолчанию: каждые 5 секунд
```swift
Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true)
```

## Разрешения macOS

Для корректной работы мониторинга активности требуются следующие разрешения:
- Доступ к событиям ввода (для отслеживания мыши и клавиатуры)
- Доступ к системной информации (для проверки состояния питания)

## Тестирование

Для тестирования функциональности используйте:
```swift
#if DEBUG
BreakTimerTests.runQuickTest()
BreakTimerTests.runAllTests()
#endif
```

## Интеграция с существующим кодом

Новая функциональность полностью интегрирована с существующим кодом:
- Автоматический запуск после завершения интервала
- Отображение в статус баре
- Сохранение в общую статистику
- Совместимость с существующими окнами и уведомлениями

## Будущие улучшения

1. Настраиваемая продолжительность отдыха
2. Различные типы отдыха (короткий/длинный)
3. Интеграция с календарем для планирования отдыха
4. Уведомления о необходимости отдыха
5. Анализ паттернов отдыха и рекомендации
