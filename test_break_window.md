# Тестирование зеленого окна напоминания

## Как быстро протестировать изменения:

### Вариант 1: Тестовый отдых (самый быстрый)
1. Запустить приложение SimplePomodoroTest
2. Кликнуть на иконку 🍅 в системном трее
3. Выбрать "🧪 Тестовый отдых (3 сек)"
4. Подождать 3 секунды - появится зеленое окно
5. Подвигать мышкой или нажать клавишу - должно появиться предупреждение "⚠️ Обнаружена активность"

### Вариант 2: Тестовый интервал + отдых
1. Запустить приложение SimplePomodoroTest
2. Кликнуть на иконку 🍅 в системном трее
3. Выбрать "🧪 Тестовый запуск (3 сек)"
4. Подождать 3 секунды - появится окно завершения интервала
5. Выбрать тип отдыха (короткий или длинный)
6. Подождать время отдыха - появится зеленое окно
7. Подвигать мышкой - должно появиться предупреждение

### Вариант 3: Настройки тестового режима
1. Открыть настройки (⌘,)
2. Включить "Режим тестирования (секунды)"
3. Установить 3 секунды
4. Сохранить
5. Запустить обычный интервал
6. Через 3 секунды будет завершение интервала

## Что проверить в зеленом окне:

✅ **Изменения которые мы сделали:**
- [ ] Зеленый градиентный фон (темно-зеленый → светло-зеленый)
- [ ] Зеленые радиальные градиенты (светло-зеленый и желтовато-зеленый)
- [ ] Новый порядок элементов: "⚠️ Обнаружена активность" → "🍃 Время отдыха!" → "Закройте ноутбук и отдохните" → Таймер → Кнопки
- [ ] Предупреждение об активности показывается по умолчанию (не скрыто)
- [ ] Таймер стал больше (шрифт 32px вместо 24px)
- [ ] Две кнопки: "Понятно" (слева) и "Скрыть на 17 мин" (справа)
- [ ] Окно увеличено до 170px по высоте для размещения всех элементов

✅ **Функциональность:**
- [ ] Таймер отсчитывает время отдыха
- [ ] При активности (движение мыши/клавиши) появляется предупреждение
- [ ] Кнопка "Понятно" работает и закрывает окно
- [ ] Окно позиционируется правильно относительно трея

## Примечания:
- Мониторинг активности проверяется каждую минуту, поэтому для тестирования предупреждения нужно подождать до минуты
- Для быстрого тестирования лучше использовать "Тестовый отдых (3 сек)"
