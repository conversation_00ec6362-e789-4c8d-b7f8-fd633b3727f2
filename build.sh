#!/bin/bash

# Простая команда для сборки и установки uProd с автоматическим обновлением версии

set -e  # Остановиться при ошибке

echo "🚀 Сборка и установка uProd..."

# 1. Обновляем версию
echo "📦 Обновление версии..."
./update_version.sh

# 2. Собираем проект
echo "🧪 Запуск автотестов..."
./test.sh

if [ $? -ne 0 ]; then
    echo "❌ Тесты провалены! Сборка остановлена."
    echo "💡 Исправьте ошибки и запустите сборку снова."
    exit 1
fi

echo "✅ Все тесты прошли успешно!"
echo ""

echo "🔨 Сборка проекта..."
xcodebuild -project SimplePomodoroTest.xcodeproj -scheme SimplePomodoroTest -configuration Release build

# 3. Устанавливаем приложение
echo "📱 Установка приложения..."
killall uProd 2>/dev/null || true
cp -r "/Users/<USER>/Library/Developer/Xcode/DerivedData/SimplePomodoroTest-ddujsqqjkqhcnpcqkpqkxcjfllxg/Build/Products/Release/uProd.app" "/Applications/"

# 4. Запускаем приложение
echo "🚀 Запуск приложения..."
open /Applications/uProd.app

echo "🎉 Готово!"
