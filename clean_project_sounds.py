#!/usr/bin/env python3

import re

def clean_sound_references():
    """Удаляет все ссылки на звуковые файлы из проекта Xcode"""
    
    project_file = "SimplePomodoroTest.xcodeproj/project.pbxproj"
    
    # Читаем файл проекта
    with open(project_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("🧹 Очищаем проект от старых ссылок на звуковые файлы...")
    
    # Удаляем все строки с .mp3 файлами
    lines = content.split('\n')
    cleaned_lines = []
    
    for line in lines:
        # Пропускаем строки с .mp3 файлами или Resources/Sounds
        if ('.mp3' in line and ('PBXFileReference' in line or 'PBXBuildFile' in line or 'in Resources' in line)) or \
           ('Resources/Sounds' in line and ('PBXFileReference' in line or 'PBXBuildFile' in line)):
            print(f"🗑️ Удаляем: {line.strip()}")
            continue
        cleaned_lines.append(line)
    
    content = '\n'.join(cleaned_lines)
    
    # Сохраняем изменения
    with open(project_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Проект очищен от старых ссылок на звуковые файлы!")
    return True

if __name__ == "__main__":
    clean_sound_references()
