#!/bin/bash

# Скрипт для автоматической сборки и установки uProd с синхронизацией версии

set -e  # Остановиться при ошибке

echo "🚀 Начинаем сборку uProd..."

# 1. Синхронизируем версию из todo.md
echo "📦 Синхронизация версии..."

TODO_FILE="todo.md"
PROJECT_FILE="SimplePomodoroTest.xcodeproj/project.pbxproj"

if [ -f "$TODO_FILE" ]; then
    # Извлекаем версию из todo.md
    VERSION=$(grep "APP_VERSION" "$TODO_FILE" | sed 's/.*APP_VERSION = \([0-9.]*\).*/\1/')
    
    if [ ! -z "$VERSION" ]; then
        echo "📦 Найдена версия $VERSION в todo.md"
        
        # Обновляем project.pbxproj
        if [ -f "$PROJECT_FILE" ]; then
            sed -i '' "s/MARKETING_VERSION = [0-9.]*;/MARKETING_VERSION = $VERSION;/g" "$PROJECT_FILE"
            echo "✅ Обновлена версия в проекте на $VERSION"
        else
            echo "❌ Файл проекта не найден: $PROJECT_FILE"
            exit 1
        fi
    else
        echo "⚠️ Версия не найдена в todo.md"
        exit 1
    fi
else
    echo "❌ Файл todo.md не найден"
    exit 1
fi

# 2. Собираем проект
echo "🔨 Сборка проекта..."
xcodebuild -project SimplePomodoroTest.xcodeproj -scheme SimplePomodoroTest -configuration Release build

if [ $? -eq 0 ]; then
    echo "✅ Сборка завершена успешно"
else
    echo "❌ Ошибка сборки"
    exit 1
fi

# 3. Устанавливаем приложение
echo "📱 Установка приложения..."

# Закрываем старую версию
killall uProd 2>/dev/null || true

# Копируем новую версию
APP_PATH="/Users/<USER>/Library/Developer/Xcode/DerivedData/SimplePomodoroTest-ddujsqqjkqhcnpcqkpqkxcjfllxg/Build/Products/Release/uProd.app"
if [ -d "$APP_PATH" ]; then
    cp -r "$APP_PATH" "/Applications/"
    echo "✅ Приложение установлено"
else
    echo "❌ Собранное приложение не найдено: $APP_PATH"
    exit 1
fi

# 4. Запускаем приложение
echo "🚀 Запуск приложения..."
open /Applications/uProd.app

echo "🎉 Готово! uProd версии $VERSION установлен и запущен"
