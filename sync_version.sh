#!/bin/bash

# Скрипт для автоматической синхронизации версии из todo.md с проектом Xcode

TODO_FILE="$(pwd)/todo.md"
PROJECT_FILE="$(pwd)/SimplePomodoroTest.xcodeproj/project.pbxproj"

# Проверяем существование файлов
if [ ! -f "$TODO_FILE" ]; then
    echo "⚠️  todo.md не найден, пропускаем синхронизацию версии"
    exit 0
fi

if [ ! -f "$PROJECT_FILE" ]; then
    echo "❌ project.pbxproj не найден"
    exit 1
fi

# Извлекаем версию из todo.md
TODO_VERSION=$(grep "APP_VERSION" "$TODO_FILE" | sed 's/.*APP_VERSION[[:space:]]*=[[:space:]]*\([0-9.]*\).*/\1/')

if [ -z "$TODO_VERSION" ]; then
    echo "⚠️  Версия не найдена в todo.md, пропускаем синхронизацию"
    exit 0
fi

# Получаем текущую версию из проекта
CURRENT_VERSION=$(grep "MARKETING_VERSION = " "$PROJECT_FILE" | head -1 | sed 's/.*MARKETING_VERSION = \([0-9.]*\);.*/\1/')

if [ "$TODO_VERSION" = "$CURRENT_VERSION" ]; then
    echo "✅ Версия уже синхронизирована: $TODO_VERSION"
    exit 0
fi

# Обновляем версию в проекте
echo "🔄 Обновление версии с $CURRENT_VERSION на $TODO_VERSION"

# Создаем резервную копию
cp "$PROJECT_FILE" "$PROJECT_FILE.backup"

# Обновляем все вхождения MARKETING_VERSION
sed -i '' "s/MARKETING_VERSION = [0-9.]*;/MARKETING_VERSION = $TODO_VERSION;/g" "$PROJECT_FILE"

# Проверяем успешность обновления
NEW_VERSION=$(grep "MARKETING_VERSION = " "$PROJECT_FILE" | head -1 | sed 's/.*MARKETING_VERSION = \([0-9.]*\);.*/\1/')

if [ "$NEW_VERSION" = "$TODO_VERSION" ]; then
    echo "✅ Версия успешно обновлена на $TODO_VERSION"
    rm "$PROJECT_FILE.backup"
else
    echo "❌ Ошибка обновления версии, восстанавливаем резервную копию"
    mv "$PROJECT_FILE.backup" "$PROJECT_FILE"
    exit 1
fi
