#!/usr/bin/env python3

import os
import uuid
import re

def add_sounds_to_xcode_project():
    """Добавляет звуковые файлы в проект Xcode"""
    
    project_file = "SimplePomodoroTest.xcodeproj/project.pbxproj"
    
    # Читаем файл проекта
    with open(project_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Находим звуковые файлы
    sound_files = []
    for root, dirs, files in os.walk("SimplePomodoroTest/Resources/Sounds"):
        for file in files:
            if file.endswith('.mp3'):
                rel_path = os.path.relpath(os.path.join(root, file), "SimplePomodoroTest")
                sound_files.append((file, rel_path))
    
    print(f"Найдено {len(sound_files)} звуковых файлов:")
    for name, path in sound_files:
        print(f"  - {name}: {path}")
    
    # Генерируем UUID для каждого файла
    file_refs = {}
    build_files = {}
    
    for name, path in sound_files:
        file_ref_uuid = str(uuid.uuid4()).replace('-', '').upper()[:24]
        build_file_uuid = str(uuid.uuid4()).replace('-', '').upper()[:24]
        
        file_refs[name] = {
            'uuid': file_ref_uuid,
            'path': path,
            'name': name
        }
        build_files[name] = {
            'uuid': build_file_uuid,
            'file_ref': file_ref_uuid
        }
    
    # Добавляем PBXFileReference секции
    file_ref_section = ""
    for name, info in file_refs.items():
        file_ref_section += f'\t\t{info["uuid"]} /* {info["name"]} */ = {{isa = PBXFileReference; lastKnownFileType = audio.mp3; path = "{info["path"]}"; sourceTree = "<group>"; }};\n'
    
    # Добавляем PBXBuildFile секции  
    build_file_section = ""
    for name, info in build_files.items():
        build_file_section += f'\t\t{info["uuid"]} /* {name} in Resources */ = {{isa = PBXBuildFile; fileRef = {info["file_ref"]} /* {name} */; }};\n'
    
    # Находим секцию PBXFileReference и добавляем наши файлы
    file_ref_pattern = r'(/\* Begin PBXFileReference section \*/.*?)(/\* End PBXFileReference section \*/)'
    if re.search(file_ref_pattern, content, re.DOTALL):
        content = re.sub(file_ref_pattern, r'\1' + file_ref_section + r'\t\t\2', content, flags=re.DOTALL)
        print("✅ Добавлены PBXFileReference секции")
    else:
        print("❌ Не найдена секция PBXFileReference")
        return False
    
    # Находим секцию PBXBuildFile и добавляем наши файлы
    build_file_pattern = r'(/\* Begin PBXBuildFile section \*/.*?)(/\* End PBXBuildFile section \*/)'
    if re.search(build_file_pattern, content, re.DOTALL):
        content = re.sub(build_file_pattern, r'\1' + build_file_section + r'\t\t\2', content, flags=re.DOTALL)
        print("✅ Добавлены PBXBuildFile секции")
    else:
        print("❌ Не найдена секция PBXBuildFile")
        return False
    
    # Находим PBXResourcesBuildPhase и добавляем наши файлы
    resources_pattern = r'(/\* Begin PBXResourcesBuildPhase section \*/.*?files = \(\s*)(.*?)(\s*\);)'
    match = re.search(resources_pattern, content, re.DOTALL)
    if match:
        existing_files = match.group(2)
        new_files = ""
        for name, info in build_files.items():
            new_files += f'\t\t\t\t{info["uuid"]} /* {name} in Resources */,\n'
        
        content = re.sub(resources_pattern, r'\1' + existing_files + new_files + r'\3', content, flags=re.DOTALL)
        print("✅ Добавлены файлы в PBXResourcesBuildPhase")
    else:
        print("❌ Не найдена секция PBXResourcesBuildPhase")
        return False
    
    # Сохраняем изменения
    with open(project_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Звуковые файлы успешно добавлены в проект!")
    print("Теперь можно собирать проект обычной командой:")
    print("xcodebuild -project SimplePomodoroTest.xcodeproj -scheme SimplePomodoroTest -configuration Debug build")
    
    return True

if __name__ == "__main__":
    add_sounds_to_xcode_project()
