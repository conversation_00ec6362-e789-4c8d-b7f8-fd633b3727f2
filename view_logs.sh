#!/bin/bash

# Скрипт для просмотра логов uProd в реальном времени

LOG_DIR="$HOME/Library/Application Support/uProd/Logs"

echo "📋 Просмотр логов uProd"
echo "======================="

# Проверяем существование директории
if [ ! -d "$LOG_DIR" ]; then
    echo "❌ Директория логов не найдена: $LOG_DIR"
    echo "Возможно, приложение еще не запускалось с новой системой логирования."
    exit 1
fi

# Находим последний лог файл
LATEST_LOG=$(ls -t "$LOG_DIR"/*.log 2>/dev/null | head -n 1)

if [ -z "$LATEST_LOG" ]; then
    echo "❌ Лог файлы не найдены в $LOG_DIR"
    exit 1
fi

echo "📁 Директория логов: $LOG_DIR"
echo "📄 Последний лог: $(basename "$LATEST_LOG")"
echo ""

# Показываем меню
echo "Выберите действие:"
echo "1) 👀 Просмотр последних 50 строк"
echo "2) 🔄 Мониторинг в реальном времени"
echo "3) 🔍 Поиск ошибок"
echo "4) 📊 Статистика логов"
echo "5) 📁 Открыть папку логов в Finder"
echo ""
read -p "Введите номер (1-5): " choice

case $choice in
    1)
        echo ""
        echo "📋 Последние 50 строк:"
        echo "====================="
        tail -n 50 "$LATEST_LOG"
        ;;
    2)
        echo ""
        echo "🔄 Мониторинг в реальном времени (Ctrl+C для выхода):"
        echo "===================================================="
        tail -f "$LATEST_LOG"
        ;;
    3)
        echo ""
        echo "🔍 Поиск ошибок и предупреждений:"
        echo "================================="
        grep -E "\[ERR\]|\[WRN\]|\[CRT\]|❌|⚠️|🚨" "$LATEST_LOG" | tail -n 20
        ;;
    4)
        echo ""
        echo "📊 Статистика логов:"
        echo "==================="
        echo "Всего строк: $(wc -l < "$LATEST_LOG")"
        echo "Ошибки: $(grep -c "\[ERR\]" "$LATEST_LOG")"
        echo "Предупреждения: $(grep -c "\[WRN\]" "$LATEST_LOG")"
        echo "Критические: $(grep -c "\[CRT\]" "$LATEST_LOG")"
        echo "Размер файла: $(du -h "$LATEST_LOG" | cut -f1)"
        echo ""
        echo "Последние события по типам:"
        echo "- Последняя ошибка:"
        grep "\[ERR\]" "$LATEST_LOG" | tail -n 1
        echo "- Последнее предупреждение:"
        grep "\[WRN\]" "$LATEST_LOG" | tail -n 1
        ;;
    5)
        echo ""
        echo "📁 Открываем папку логов в Finder..."
        open "$LOG_DIR"
        ;;
    *)
        echo "❌ Неверный выбор"
        exit 1
        ;;
esac
